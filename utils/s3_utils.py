"""
s3_utils.py

This module contains utility functions for direct S3 file operations.
Since we're using a mounted S3 path, we'll use standard file operations.
"""

import os
import logging
import shutil
import subprocess
from typing import List, Optional

import config

# Use S3 configuration from config.py
# Sudo is required for S3 operations
USE_SUDO_FOR_S3 = config.S3_USE_SUDO

logger = logging.getLogger(__name__)

def run_sudo_command(cmd, error_msg="Command failed", use_sudo=USE_SUDO_FOR_S3):
    """
    Run a command with sudo and return the result.

    Args:
        cmd: List of command arguments
        error_msg: Error message to log if the command fails
        use_sudo: Whether to use sudo or not

    Returns:
        tuple: (success, output/error message)
    """
    if not use_sudo:
        # Remove sudo from the command if we're not using sudo
        if cmd[0] == "sudo":
            cmd = cmd[1:]

    # Skip empty commands
    if not cmd:
        return False, "Empty command"
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            return True, result.stdout.strip()
        else:
            logger.error(f"{error_msg}: {result.stderr}")
            logger.error(f"Command failed with return code {result.returncode}: {' '.join(cmd)}")
            return False, result.stderr
    except Exception as e:
        logger.error(f"{error_msg} - Exception: {e}")
        logger.error(f"Exception while running command: {' '.join(cmd)}")
        return False, str(e)

def upload_file_to_s3(
    local_file_path: str,
    book_id: str,
    chapter_id: str,
    res_id: str,
    file_name: Optional[str] = None,
    is_quiz_image: bool = True
) -> str:
    """
    Upload a file directly to S3 using the mounted path.

    Args:
        local_file_path: Path to the local file to upload
        book_id: Book identifier
        chapter_id: Chapter identifier
        res_id: Resource identifier
        file_name: Optional custom filename (if None, uses the original filename)
        is_quiz_image: Whether this is a quiz image (determines the path)

    Returns:
        str: The S3 path where the file was uploaded
    """
    if not os.path.exists(local_file_path):
        logger.error(f"File not found: {local_file_path}")
        return ""

    # Determine the target filename
    if file_name is None:
        file_name = os.path.basename(local_file_path)

    # Construct the S3 path
    if file_name == "extracted_data.txt":
        # For output.json, put it directly in the resId folder
        s3_dir = f"supload/pdfextracts/{book_id}/{chapter_id}/{res_id}"
    elif is_quiz_image:
        s3_dir = f"supload/pdfextracts/{book_id}/{chapter_id}/{res_id}/extractedQuizImages"
    else:
        s3_dir = f"supload/pdfextracts/{book_id}/{chapter_id}/{res_id}/extractedImages"

    # Create the full path with the S3 mount
    full_s3_dir = os.path.join(config.S3_MOUNT_PATH, s3_dir)

    # Check if we need to create the directory
    # Note: Directories like 'supload' and 'pdfextracts' may already exist
    try:
        # First check if the directory already exists
        check_success, _ = run_sudo_command(
            ["sudo", "test", "-d", full_s3_dir],
            f"Checking if directory exists: {full_s3_dir}"
        )

        if not check_success:
            # Directory doesn't exist, create it
            # Create only the final directory (extractedQuizImages or extractedImages)
            # since parent directories like 'supload' and 'pdfextracts' should already exist
            success, message = run_sudo_command(
                ["sudo", "mkdir", "-p", full_s3_dir],
                f"Failed to create S3 directory {full_s3_dir}"
            )

            if not success:
                logger.error(f"CRITICAL ERROR: Sudo mkdir failed. S3 upload cannot proceed without sudo access.")
                logger.error(f"Please ensure the application has sudo privileges for mkdir operations.")
                return ""
    except Exception as dir_err:
        logger.error(f"Failed to create/check S3 directory {full_s3_dir}: {dir_err}")
        return ""

    # Full path for the destination file
    s3_file_path = os.path.join(full_s3_dir, file_name)

    try:
        # Use sudo to copy the file
        success, message = run_sudo_command(
            ["sudo", "cp", local_file_path, s3_file_path],
            f"Failed to copy file to {s3_file_path}"
        )

        if not success:
            logger.error(f"CRITICAL ERROR: Sudo cp failed. S3 upload cannot proceed without sudo access.")
            logger.error(f"Please ensure the application has sudo privileges for cp operations.")
            return ""

        # Verify the file was copied successfully using sudo
        file_exists = False
        file_size_str = "unknown"

        # Verify with sudo ls
        verify_success, verify_message = run_sudo_command(
            ["sudo", "ls", "-l", s3_file_path],
            f"Failed to verify file at {s3_file_path}"
        )

        if verify_success:
            file_exists = True
        else:
            logger.error(f"CRITICAL ERROR: Cannot verify file at {s3_file_path}. Sudo ls failed.")
            logger.error(f"Please ensure the application has sudo privileges for ls operations.")

        if file_exists:
            # Return the relative S3 path (without the mount prefix)
            relative_path = os.path.join(s3_dir, file_name)
            return relative_path
        else:
            logger.error(f"File not found at destination after copy: {s3_file_path}")
            return ""
    except Exception as e:
        logger.error(f"Error uploading file to S3: {e}")
        return ""

def read_file_from_s3(s3_file_path):
    """
    Read a file from the S3 mount path using sudo.

    Args:
        s3_file_path: Full path to the file in the S3 mount

    Returns:
        bytes: File content as bytes, or None if the file could not be read
    """
    # First check if the file exists
    success, _ = run_sudo_command(
        ["sudo", "test", "-f", s3_file_path],
        f"File not found: {s3_file_path}"
    )

    if not success:
        logger.error(f"CRITICAL ERROR: File not found or not accessible: {s3_file_path}")
        return None

    # Create a temporary file to store the content
    import tempfile
    temp_file = tempfile.NamedTemporaryFile(delete=False)
    temp_path = temp_file.name
    temp_file.close()

    try:
        # Copy the file to the temporary location using sudo
        success, message = run_sudo_command(
            ["sudo", "cp", s3_file_path, temp_path],
            f"Failed to copy S3 file to temporary location"
        )

        if not success:
            logger.error(f"CRITICAL ERROR: Could not copy file from S3 to temporary location")
            return None

        # Make the temporary file readable
        run_sudo_command(
            ["sudo", "chmod", "644", temp_path],
            f"Failed to make temporary file readable"
        )

        # Read the file content
        with open(temp_path, "rb") as f:
            content = f.read()

        return content
    except Exception as e:
        logger.error(f"Error reading file from S3: {e}")
        return None
    finally:
        # Clean up the temporary file
        try:
            os.unlink(temp_path)
        except:
            pass

def get_s3_path(relative_path):
    """
    Get the full S3 path for a relative path.

    Args:
        relative_path: Relative path within the S3 mount

    Returns:
        str: Full path including the S3 mount prefix
    """
    return os.path.join(config.S3_MOUNT_PATH, relative_path)

def get_s3_file_size(s3_file_path):
    """
    Get the size of a file in S3 using sudo.

    Args:
        s3_file_path: Full path to the file in the S3 mount

    Returns:
        int: File size in bytes, or None if the file could not be accessed
    """
    try:
        # Use sudo stat to get file size
        success, output = run_sudo_command(
            ["sudo", "stat", "-c", "%s", s3_file_path],
            f"Failed to get file size for {s3_file_path}"
        )

        if success and output.strip().isdigit():
            return int(output.strip())
        else:
            logger.warning(f"Could not determine file size for {s3_file_path}")
            return None

    except Exception as e:
        logger.error(f"Error getting file size from S3: {e}")
        return None

def upload_images_to_s3(
    image_paths: List[str],
    book_id: str,
    chapter_id: str,
    res_id: str,
    is_quiz_images: bool = True
) -> List[str]:
    """
    Upload multiple images to S3 using the mounted path.

    Args:
        image_paths: List of local image paths to upload
        book_id: Book identifier
        chapter_id: Chapter identifier
        res_id: Resource identifier
        is_quiz_images: Whether these are quiz images

    Returns:
        List[str]: List of S3 paths where the images were uploaded
    """
    uploaded_paths = []
    failed_uploads = 0

    for image_path in image_paths:
        if not os.path.exists(image_path):
            logger.warning(f"Image not found: {image_path}")
            failed_uploads += 1
            continue

        s3_path = upload_file_to_s3(
            image_path,
            book_id,
            chapter_id,
            res_id,
            file_name=None,  # Use original filename
            is_quiz_image=is_quiz_images
        )

        if s3_path:
            uploaded_paths.append(s3_path)
        else:
            logger.error(f"Failed to upload image: {image_path}")
            failed_uploads += 1

    if failed_uploads > 0:
        logger.error(f"Batch upload had {failed_uploads} failures out of {len(image_paths)} images")

    return uploaded_paths


def write_text_to_s3(
    content: str,
    book_id: str,
    chapter_id: str,
    res_id: str,
    file_name: str,
    subfolder: str = "extractedImages"
) -> str:
    """
    Write text content directly to S3 without creating a local file first.

    Args:
        content: Text content to write
        book_id: Book identifier
        chapter_id: Chapter identifier
        res_id: Resource identifier
        file_name: Name of the file to create
        subfolder: Subfolder within the resource directory (extractedImages, extractedQuizImages, text_extraction)

    Returns:
        str: The S3 path where the file was written, empty string if failed
    """
    import tempfile

    try:
        # Create a temporary file to write content
        with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', delete=False, suffix='.txt') as temp_file:
            temp_file.write(content)
            temp_file_path = temp_file.name

        # Construct the S3 path
        s3_dir = f"supload/pdfextracts/{book_id}/{chapter_id}/{res_id}/{subfolder}"
        full_s3_dir = os.path.join(config.S3_MOUNT_PATH, s3_dir)

        # Create directory if it doesn't exist
        try:
            check_success, _ = run_sudo_command(
                ["sudo", "test", "-d", full_s3_dir],
                f"Checking if directory exists: {full_s3_dir}"
            )

            if not check_success:
                success, message = run_sudo_command(
                    ["sudo", "mkdir", "-p", full_s3_dir],
                    f"Failed to create S3 directory {full_s3_dir}"
                )
                if not success:
                    logger.error(f"Failed to create S3 directory: {full_s3_dir}")
                    return ""
        except Exception as dir_err:
            logger.error(f"Failed to create/check S3 directory {full_s3_dir}: {dir_err}")
            return ""

        # Full path for the destination file
        s3_file_path = os.path.join(full_s3_dir, file_name)

        # Copy the temporary file to S3
        success, message = run_sudo_command(
            ["sudo", "cp", temp_file_path, s3_file_path],
            f"Failed to copy file to {s3_file_path}"
        )

        if not success:
            logger.error(f"Failed to write text file to S3: {s3_file_path}")
            return ""

        # Verify the file was written
        verify_success, _ = run_sudo_command(
            ["sudo", "test", "-f", s3_file_path],
            f"Failed to verify file at {s3_file_path}"
        )

        if verify_success:
            # Return the relative S3 path
            relative_path = os.path.join(s3_dir, file_name)
            logger.info(f"Successfully wrote text file to S3: {relative_path}")
            return relative_path
        else:
            logger.error(f"File verification failed at: {s3_file_path}")
            return ""

    except Exception as e:
        logger.error(f"Error writing text to S3: {e}")
        return ""
    finally:
        # Clean up temporary file
        try:
            if 'temp_file_path' in locals():
                os.unlink(temp_file_path)
        except:
            pass


def list_files_in_s3_directory(
    book_id: str,
    chapter_id: str,
    res_id: str,
    subfolder: str = "extractedImages",
    file_pattern: str = "*.txt"
) -> List[str]:
    """
    List files in an S3 directory.

    Args:
        book_id: Book identifier
        chapter_id: Chapter identifier
        res_id: Resource identifier
        subfolder: Subfolder within the resource directory
        file_pattern: File pattern to match (e.g., "*.txt", "*.json")

    Returns:
        List[str]: List of file paths found in the directory
    """
    try:
        if subfolder:
            s3_dir = f"supload/pdfextracts/{book_id}/{chapter_id}/{res_id}/{subfolder}"
        else:
            s3_dir = f"supload/pdfextracts/{book_id}/{chapter_id}/{res_id}"
        full_s3_dir = os.path.join(config.S3_MOUNT_PATH, s3_dir)

        # Check if directory exists
        check_success, _ = run_sudo_command(
            ["sudo", "test", "-d", full_s3_dir],
            f"Checking if directory exists: {full_s3_dir}"
        )

        if not check_success:
            logger.warning(f"S3 directory does not exist: {full_s3_dir}")
            return []

        # List files in the directory
        success, output = run_sudo_command(
            ["sudo", "find", full_s3_dir, "-name", file_pattern, "-type", "f"],
            f"Failed to list files in {full_s3_dir}"
        )

        if success and output.strip():
            # Convert full paths to relative paths
            file_paths = []
            for full_path in output.strip().split('\n'):
                if full_path.strip():
                    # Convert to relative path
                    relative_path = os.path.relpath(full_path, config.S3_MOUNT_PATH)
                    file_paths.append(relative_path)
            return file_paths
        else:
            return []

    except Exception as e:
        logger.error(f"Error listing files in S3 directory: {e}")
        return []
