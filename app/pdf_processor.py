# pdf_processor.py

import logging
import os

from utils.pdf_utils import extract_text_from_pdf, extract_text_from_pdf_new
from utils.langchain_utils import break_text_into_chunks, break_text_into_chunks_admin
from utils.pinecone_utils import manage_index_and_namespace
from services.response_service import generate_response_content_text

logger = logging.getLogger(__name__)


def process_pdf(file_name, file_data, index_name, user_type):
    """
    Function to process a PDF file and store its content into Pinecone.
    It extracts text from the PDF, breaks the text into chunks, and stores the chunks in Pinecone.

    Args:
        file_name (str): The name of the file, used as the namespace in Pinecone.
        file_data (bytes): The data of the file in bytes.
        index_name (str): The data of the file in bytes.
        user_type (str): The data of the file in bytes.

    """
    logger.info(f"Processing PDF: {file_name}")
    namespace = file_name

    # Extract text from PDF
    text = extract_text_from_pdf(file_data)
    logger.info(f"Extracted text from PDF: {file_name}")

    # Break text into chunks
    chunks = break_text_into_chunks(text)
    logger.info(f"Text broken into chunks: {len(chunks)}")

    # Store chunks in Pinecone
    index = index_name

    logger.info(f"Storing chunks in Pinecone")
    manage_index_and_namespace(index, chunks, namespace, user_type)


async def process_pdf_new(file_path, namespace, index_name, user_type, res_id=None):
    """
    Function to process a PDF file and store its content.
    For admin users, it stores the full text without chunking.
    For other users, it extracts text from the PDF, breaks the text into chunks, and stores the chunks in Pinecone.

    Args:
        file_path (str): The path of the file.
        namespace (str): The namespace of the file.
        index_name (str): The data of the file.
        user_type (str): The data of the file.
        res_id (str, optional): The resource ID. If provided, it will be used to extract text.

    Returns:
        bool: True if processing was successful, None if there was an error.
    """
    try:
        logger.info(f"Processing PDF: {namespace}")
        namespace = namespace

        # Extract text from PDF
        logger.info(f"Extracting text using resource ID: {res_id}")
        text = await extract_text_from_pdf_new(res_id)

        # If text extraction failed, return None
        if text is None:
            logger.error(f"Text extraction failed for resource ID: {res_id}")
            return None

        file_name = os.path.basename(file_path)
        logger.info(f"Extracted text from PDF: {file_name}")

        # For admin users, store full text without chunking
        if user_type.lower() == "admin":
            logger.info(f"Storing full text for admin user (no chunking)")
            # Store the full text as a single document
            manage_index_and_namespace(index_name, [text], namespace, user_type)
        else:
            # Break text into chunks for non-admin users
            chunks = []
            if user_type.lower() == "user":
                chunks = break_text_into_chunks(text)
            elif user_type.lower() == "drive":
                chunks = break_text_into_chunks_admin(text)
            logger.info(f"Text broken into chunks: {len(chunks)}")

            # Store chunks in Pinecone
            index = index_name

            logger.info(f"Storing chunks")
            manage_index_and_namespace(index, chunks, namespace, user_type)

        logger.info(f"PDF processing complete: {namespace}")
        return True
    except Exception as e:
        import traceback
        logger.error(f"Error in process_pdf_new: {e}")
        logger.error(traceback.format_exc())
        return None


def text_processor(text_data, query, res_type):
    logger.info(f"Inside text_processor")
    logger.info(f"Full text length: {len(text_data)} characters")
    logger.info(f"res_type: {res_type}")

    # Pass full text directly instead of chunking
    response = generate_response_content_text(query, text_data, res_type)

    if len(response) > 0:
        logger.info(f"Data retrieved successfully for txt file")
    else:
        logger.info(f"No data retrieved for txt file")
    return response


def process_ext_content(site_content, index_name, user_type, namespace):
    chunks = break_text_into_chunks(site_content)
    logger.info(f"Text broken into chunks: {len(chunks)}")
    manage_index_and_namespace(index_name, chunks, namespace, user_type)

