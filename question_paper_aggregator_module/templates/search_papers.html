{% extends "base.html" %}

{% block title %}Search Question Papers | {{ exam['exam_name'] }} | PYQs Admin | GPT Sir{% endblock %}

{% block head %}
<style>
  .pyqs-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .pyqs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
  }

  .pyqs-header h1 {
    font-size: 1.775rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
  }

  .back-btn {
    background-color: #f3f4f6;
    color: #374151;
    padding: 12px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
    border: 1px solid #e5e7eb;
  }

  .back-btn:hover {
    background-color: #e5e7eb;
    transform: translateY(-1px);
  }

  .search-form {
    background-color: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    padding: 24px;
    margin-bottom: 32px;
  }

  .form-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #111827;
  }

  .exam-info {
    background-color: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;
  }

  .exam-info h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #0c4a6e;
    margin: 0 0 8px 0;
  }

  .exam-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
  }

  .exam-detail {
    font-size: 0.875rem;
    color: #0369a1;
  }

  .exam-detail strong {
    color: #0c4a6e;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
  }

  .form-group {
    margin-bottom: 16px;
  }

  .form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    font-size: 0.875rem;
    color: #374151;
  }

  .form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    background-color: #f9fafb;
    transition: border-color 0.2s, background-color 0.2s;
  }

  .form-control:focus {
    outline: none;
    border-color: #10b981;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }

  .search-btn {
    background-color: #3b82f6;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .search-btn:hover {
    background-color: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .search-btn:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .progress-container {
    background-color: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    padding: 24px;
    margin-bottom: 32px;
    /* Override any problematic inherited styles */
    max-width: none !important;
    width: auto !important;
    height: auto !important;
    min-height: auto !important;
  }

  .progress-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #111827;
  }

  .progress-log {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    max-height: 400px;
    overflow-y: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .log-entry {
    margin-bottom: 8px;
    padding: 4px 0;
  }

  .log-entry.status-started { color: #059669; }
  .log-entry.status-processing { color: #0369a1; }
  .log-entry.status-downloading { color: #7c3aed; }
  .log-entry.status-verified { color: #059669; font-weight: 600; }
  .log-entry.status-uploaded { color: #059669; font-weight: 600; }
  .log-entry.status-error { color: #dc2626; }
  .log-entry.status-complete { color: #059669; font-weight: 600; }

  .results-container {
    background-color: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    padding: 24px;
    display: none;
  }

  .results-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #111827;
  }

  .result-item {
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 12px;
  }

  .result-item.uploaded {
    background-color: #f0fdf4;
    border-color: #bbf7d0;
  }

  .result-item.not-found {
    background-color: #fef2f2;
    border-color: #fecaca;
  }

  .result-year {
    font-weight: 600;
    color: #111827;
    margin-bottom: 4px;
  }

  .result-status {
    font-size: 0.875rem;
    color: #6b7280;
  }

  .result-status.uploaded {
    color: #059669;
    font-weight: 500;
  }

  .result-status.not-found {
    color: #dc2626;
  }

  .completion-summary {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    text-align: center;
  }

  .completion-summary.error {
    background-color: #fef2f2;
    border-color: #fecaca;
  }

  .completion-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #059669;
    margin-bottom: 12px;
  }

  .completion-title.error {
    color: #dc2626;
  }

  .completion-message {
    color: #065f46;
    margin-bottom: 16px;
    line-height: 1.5;
  }

  .completion-message.error {
    color: #991b1b;
  }

  .completion-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .completion-btn {
    background-color: #059669;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
  }

  .completion-btn:hover {
    background-color: #047857;
    transform: translateY(-1px);
  }

  .completion-btn.secondary {
    background-color: #6b7280;
  }

  .completion-btn.secondary:hover {
    background-color: #4b5563;
  }

  /* New completion summary styles */
  .completion-summary-content {
    text-align: center;
  }

  .results-breakdown {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-bottom: 24px;
    flex-wrap: wrap;
  }

  .result-stat {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    min-width: 120px;
    text-align: center;
  }

  .result-stat.success {
    background-color: #f0fdf4;
    border-color: #bbf7d0;
  }

  .result-stat.warning {
    background-color: #fffbeb;
    border-color: #fed7aa;
  }

  .result-stat.neutral {
    background-color: #f8fafc;
    border-color: #e2e8f0;
  }

  .stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 4px;
  }

  .result-stat.success .stat-number {
    color: #059669;
  }

  .result-stat.warning .stat-number {
    color: #d97706;
  }

  .stat-label {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
  }

  .summary-message {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;
    text-align: left;
  }

  .summary-message p {
    margin-bottom: 12px;
    line-height: 1.6;
  }

  .summary-message p:last-child {
    margin-bottom: 0;
  }

  .summary-message ul {
    margin: 12px 0;
    padding-left: 20px;
  }

  .summary-message li {
    margin-bottom: 8px;
    line-height: 1.5;
  }

  .completion-btn.primary {
    background-color: #059669;
  }

  .completion-btn.primary:hover {
    background-color: #047857;
  }

  @media (max-width: 768px) {
    .pyqs-container {
      padding: 16px;
    }

    .pyqs-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .form-grid {
      grid-template-columns: 1fr;
    }

    .exam-details {
      grid-template-columns: 1fr;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="pyqs-container">
  <div class="pyqs-header">
    <h1>Search Question Papers</h1>
    <a href="/pyqs_admin/exams/{{ exam['id'] }}/documents" class="back-btn">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
      </svg>
      Back to Documents
    </a>
  </div>

  <div class="search-form">
    <h2 class="form-title">Intelligent Question Paper Search</h2>
    
    <div class="exam-info">
      <h3>Exam Details</h3>
      <div class="exam-details">
        <div class="exam-detail"><strong>Name:</strong> {{ exam['exam_name'] }}</div>
        <div class="exam-detail"><strong>Level:</strong> {{ exam['level'] }}</div>
        <div class="exam-detail"><strong>Syllabus:</strong> {{ exam['syllabus'] }}</div>
        <div class="exam-detail"><strong>Grade:</strong> {{ exam['grade'] }}</div>
        <div class="exam-detail"><strong>Subject:</strong> {{ exam['subject'] }}</div>
      </div>
    </div>

    <form id="searchForm">
      <input type="hidden" id="examId" value="{{ exam['id'] }}">
      <div class="form-grid">
        <div class="form-group">
          <label for="startingYear">Starting Year <span style="color: #dc2626;">*</span></label>
          <input type="number" id="startingYear" class="form-control" min="1990" max="2030" required>
          <small style="color: #6b7280; font-size: 0.75rem; margin-top: 4px; display: block;">
            Year to start searching from (e.g., 2025)
          </small>
        </div>
        <div class="form-group">
          <label for="numYears">Number of Years to Search</label>
          <input type="number" id="numYears" class="form-control" value="5" min="1" max="30" required>
          <small style="color: #6b7280; font-size: 0.75rem; margin-top: 4px; display: block;">
            Search backwards from starting year (1-30 years)
          </small>
        </div>
        <div class="form-group">
          <label for="language">Preferred Language (Optional)</label>
          <input type="text" id="language" class="form-control" placeholder="e.g., English, Hindi">
          <small style="color: #6b7280; font-size: 0.75rem; margin-top: 4px; display: block;">
            Leave empty to search in any language
          </small>
        </div>
      </div>
      <button type="submit" class="search-btn" id="searchButton">
        <span class="spinner" id="searchSpinner" style="display: none;"></span>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" id="searchIcon">
          <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
        </svg>
        <span id="buttonText">Start Intelligent Search</span>
      </button>
    </form>
  </div>

  <div class="progress-container" id="progressContainer">
    <h3 class="progress-title" id="progressTitle">Search Progress</h3>
    <div class="progress-content" id="progressContent">
      <div class="progress-log" id="progressLog">
        <div class="log-entry" id="defaultMessage" style="color: #6b7280; font-style: italic;">
          Ready to start intelligent search. Click "Start Intelligent Search" button above to begin.
        </div>
      </div>
    </div>
  </div>
</div>

<script src="/question-paper-static/js/search.js"></script>
{% endblock %}
