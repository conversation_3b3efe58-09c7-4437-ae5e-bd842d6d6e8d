"""
Core functionality modules for Question Paper Aggregator
"""

from .searcher import find_question_paper, download_pdf, find_question_paper_without_year
from .verifier import check_with_gpt_vision, extract_first_page_as_image, OPENAI_API_KEY

__all__ = [
    'find_question_paper',
    'download_pdf', 
    'find_question_paper_without_year',
    'check_with_gpt_vision',
    'extract_first_page_as_image',
    'OPENAI_API_KEY'
]
