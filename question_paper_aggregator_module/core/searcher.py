import googlesearch
import requests
import os
import logging

logger = logging.getLogger(__name__)

def find_question_paper(exam_name, year, subject, language=None, start_index=0, num_results=5):
    """Searches for multiple question paper PDFs and returns a list of valid URLs."""
    # Use user inputs directly in search query
    query = f"{exam_name} {year} {subject} previous year question paper filetype:pdf"

    if language:
        query = f"{exam_name} {year} {subject} previous year question paper {language} filetype:pdf"

    # Print the search query with pagination info
    if start_index == 0:
        logger.info(f"🔍 GOOGLE SEARCH QUERY: {query}")
    else:
        logger.info(f"🔍 GOOGLE SEARCH QUERY (Results {start_index+1}-{start_index+num_results}): {query}")

    pdf_links = []

    try:
        # Get more results and slice them for pagination
        total_results_needed = start_index + num_results
        results = list(googlesearch.search(query, num_results=total_results_needed))

        # Slice to get the specific batch
        batch_results = results[start_index:start_index + num_results]

        for link in batch_results:
            if link.endswith(".pdf"):
                pdf_links.append(link)
                logger.info(f"🔍 Found PDF: {link}")

    except Exception as e:
        logger.error(f"Error fetching Google results: {e}")

    return pdf_links  # Ensure this is always a **LIST**


def find_question_paper_without_year(exam_name, subject, language=None):
    """Searches for question papers without specifying year (fallback search)."""
    # Use user inputs directly in search query without year
    query = f"{exam_name} {subject} previous year question paper filetype:pdf"

    if language:
        query = f"{exam_name} {subject} previous year question paper {language} filetype:pdf"

    # Print the search query
    logger.info(f"🔍 FALLBACK GOOGLE SEARCH QUERY (NO YEAR): {query}")

    pdf_links = []

    try:
        results = list(googlesearch.search(query, num_results=5))  # Get top 5 results
        for link in results:
            if link.endswith(".pdf"):
                pdf_links.append(link)
                logger.info(f"🔍 Found PDF: {link}")

    except Exception as e:
        logger.error(f"Error fetching Google results: {e}")

    return pdf_links  # Ensure this is always a **LIST**


def download_pdf(url, save_path):
    """Downloads a PDF file from a given URL."""
    try:
        response = requests.get(url, stream=True)
        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(1024):
                    f.write(chunk)
            logger.info(f"📥 Downloaded: {save_path}")
            return save_path
    except Exception as e:
        logger.error(f"Error downloading {url}: {e}")
    return None
