"""
Notification Service for real-time updates
Handles all communication with the frontend via Server-Sent Events
"""

import queue
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class NotificationService:
    """Service for managing real-time notifications to clients"""
    
    def __init__(self, update_queues: Dict[str, queue.Queue]):
        self.update_queues = update_queues
    
    def send_update(self, session_id: str, update: Dict[str, Any]) -> None:
        """Send an update to a specific session"""
        logger.info(f"📤 SENDING UPDATE - Session: {session_id} - Status: {update.get('status')} - Message: {update.get('message', '')}")
        if session_id in self.update_queues:
            self.update_queues[session_id].put(update)
            logger.info(f"✅ UPDATE QUEUED - Session: {session_id} - Queue size: {self.update_queues[session_id].qsize()}")
        else:
            logger.warning(f"⚠️ SESSION NOT FOUND - Session: {session_id} - Available sessions: {list(self.update_queues.keys())}")
    
    def send_process_started(self, session_id: str, exam_name: str, subject: str, num_years: int, language: Optional[str]) -> None:
        """Send process started notification"""
        self.send_update(session_id, {
            'status': 'started',
            'message': f"Starting search for {exam_name} {subject} papers for the last {num_years} years"
        })
    
    def send_year_processing(self, session_id: str, year: str) -> None:
        """Send year processing notification"""
        self.send_update(session_id, {
            'status': 'processing_year',
            'year': year,
            'message': f"Processing year {year}"
        })
    
    def send_subject_searching(self, session_id: str, year: str, subject: str) -> None:
        """Send subject searching notification"""
        self.send_update(session_id, {
            'status': 'searching_subject',
            'year': year,
            'subject': subject,
            'message': f"Searching for {subject} paper ({year})"
        })
    
    def send_not_found(self, session_id: str, year: str, subject: str) -> None:
        """Send not found notification"""
        self.send_update(session_id, {
            'status': 'not_found',
            'year': year,
            'subject': subject,
            'message': f"No question papers found for {subject} ({year})"
        })
    
    def send_found_links(self, session_id: str, year: str, subject: str, count: int) -> None:
        """Send found links notification"""
        self.send_update(session_id, {
            'status': 'found_links',
            'year': year,
            'subject': subject,
            'count': count,
            'message': f"Found {count} potential papers for {subject} ({year})"
        })
    
    def send_downloading(self, session_id: str, year: str, subject: str, url: str, index: int, total: int) -> None:
        """Send downloading notification"""
        self.send_update(session_id, {
            'status': 'downloading',
            'year': year,
            'subject': subject,
            'url': url,
            'index': index,
            'total': total,
            'message': f"Downloading paper {index}/{total} for {subject} ({year})"
        })
    
    def send_downloaded(self, session_id: str, year: str, subject: str, file_path: str) -> None:
        """Send downloaded notification"""
        self.send_update(session_id, {
            'status': 'downloaded',
            'year': year,
            'subject': subject,
            'file_path': file_path,
            'message': f"Downloaded paper for {subject} ({year}). Verifying..."
        })
    
    def send_verifying(self, session_id: str, year: str, subject: str) -> None:
        """Send verifying notification"""
        self.send_update(session_id, {
            'status': 'verifying',
            'year': year,
            'subject': subject,
            'message': f"Verifying paper for {subject} ({year})..."
        })
    
    def send_verified(self, session_id: str, year: str, subject: str, file_path: str) -> None:
        """Send verified notification"""
        self.send_update(session_id, {
            'status': 'verified',
            'year': year,
            'subject': subject,
            'file_path': file_path,
            'message': f"Verified paper found for {subject} ({year})",
            'result': {
                'subject': subject,
                'year': year,
                'file_path': file_path,
                'status': 'found'
            }
        })
    
    def send_not_verified(self, session_id: str, year: str, subject: str) -> None:
        """Send not verified notification"""
        self.send_update(session_id, {
            'status': 'not_verified',
            'year': year,
            'subject': subject,
            'message': f"Paper verification failed for {subject} ({year}). Trying next..."
        })
    
    def send_subject_complete(self, session_id: str, year: str, subject: str, result: str) -> None:
        """Send subject complete notification"""
        self.send_update(session_id, {
            'status': 'subject_complete',
            'year': year,
            'subject': subject,
            'result': result,
            'message': f"No verified papers found for {subject} ({year})"
        })
    
    def send_year_complete(self, session_id: str, year: str, count: int) -> None:
        """Send year complete notification"""
        self.send_update(session_id, {
            'status': 'year_complete',
            'year': year,
            'count': count,
            'message': f"Completed processing for year {year}"
        })
    
    def send_error(self, session_id: str, year: str, message: str) -> None:
        """Send error notification"""
        self.send_update(session_id, {
            'status': 'error',
            'year': year,
            'message': message
        })
    
    def send_fallback_search(self, session_id: str, exam_name: str, subject: str) -> None:
        """Send fallback search notification"""
        self.send_update(session_id, {
            'status': 'fallback_search',
            'message': f"No verified papers found. Trying fallback search without year for {exam_name} {subject}..."
        })
    
    def send_fallback_found_links(self, session_id: str, subject: str, count: int) -> None:
        """Send fallback found links notification"""
        self.send_update(session_id, {
            'status': 'fallback_found_links',
            'subject': subject,
            'count': count,
            'message': f"Found {count} potential papers in fallback search for {subject}"
        })
    
    def send_fallback_downloading(self, session_id: str, subject: str, url: str, index: int, total: int) -> None:
        """Send fallback downloading notification"""
        self.send_update(session_id, {
            'status': 'fallback_downloading',
            'subject': subject,
            'url': url,
            'index': index,
            'total': total,
            'message': f"Downloading fallback paper {index}/{total} for {subject}"
        })
    
    def send_fallback_downloaded(self, session_id: str, subject: str, file_path: str) -> None:
        """Send fallback downloaded notification"""
        self.send_update(session_id, {
            'status': 'fallback_downloaded',
            'subject': subject,
            'file_path': file_path,
            'message': f"Downloaded fallback paper for {subject}. Verifying..."
        })
    
    def send_fallback_verifying(self, session_id: str, subject: str) -> None:
        """Send fallback verifying notification"""
        self.send_update(session_id, {
            'status': 'fallback_verifying',
            'subject': subject,
            'message': f"Verifying fallback paper for {subject}..."
        })
    
    def send_fallback_verified(self, session_id: str, subject: str, file_path: str) -> None:
        """Send fallback verified notification"""
        self.send_update(session_id, {
            'status': 'fallback_verified',
            'subject': subject,
            'file_path': file_path,
            'message': f"Verified fallback paper found for {subject}",
            'result': {
                'subject': subject,
                'year': 'Unknown',
                'file_path': file_path,
                'status': 'found'
            }
        })
    
    def send_fallback_not_verified(self, session_id: str, subject: str) -> None:
        """Send fallback not verified notification"""
        self.send_update(session_id, {
            'status': 'fallback_not_verified',
            'subject': subject,
            'message': f"Fallback paper verification failed for {subject}. Trying next..."
        })
    
    def send_fallback_failed(self, session_id: str, subject: str) -> None:
        """Send fallback failed notification"""
        self.send_update(session_id, {
            'status': 'fallback_failed',
            'subject': subject,
            'message': f"Fallback search found no papers for {subject}"
        })
    
    def send_fallback_error(self, session_id: str, message: str) -> None:
        """Send fallback error notification"""
        self.send_update(session_id, {
            'status': 'fallback_error',
            'message': message
        })
    
    def send_process_complete(self, session_id: str, results: list, count: int) -> None:
        """Send process complete notification"""
        self.send_update(session_id, {
            'status': 'complete',
            'count': count,
            'results': results,
            'message': f"Search complete. Found {count} results."
        })
    
    def send_upload_started(self, session_id: str, file_path: str, year: int, month: str = None, shift: str = None) -> None:
        """Send upload started notification"""
        self.send_update(session_id, {
            'status': 'upload_started',
            'file_path': file_path,
            'year': year,
            'month': month,
            'shift': shift,
            'message': f"Starting upload for {file_path} ({year})"
        })
    
    def send_upload_success(self, session_id: str, file_path: str, year: int) -> None:
        """Send upload success notification"""
        self.send_update(session_id, {
            'status': 'upload_success',
            'file_path': file_path,
            'year': year,
            'message': f"Successfully uploaded {file_path} ({year})"
        })
    
    def send_upload_error(self, session_id: str, file_path: str, error: str) -> None:
        """Send upload error notification"""
        self.send_update(session_id, {
            'status': 'upload_error',
            'file_path': file_path,
            'error': error,
            'message': f"Failed to upload {file_path}: {error}"
        })
