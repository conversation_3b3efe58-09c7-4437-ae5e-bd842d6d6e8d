"""
API endpoints for Question Paper Aggregator Module
Integrated with PYQs Admin functionality
"""

from fastapi import APIRouter, Depends, Request, HTTPException, BackgroundTasks
from fastapi.responses import HTMLResponse, JSONResponse, StreamingResponse
from pydantic import BaseModel
from typing import Optional, Dict
import os
import json
import queue
import logging
import datetime
import uuid

from auth.dependencies import require_login
from auth.rbac import require_roles
from db_config.pyqs_admin_db import get_exam_by_id
from ..services import SearchService, NotificationService

logger = logging.getLogger(__name__)

# Create API router
api_router = APIRouter()

# Global queue for real-time updates
update_queues: Dict[str, queue.Queue] = {}

def cleanup_all_sessions():
    """Clean up all existing sessions - useful for server restart"""
    if update_queues:
        logger.info(f"🧹 STARTUP CLEANUP - Clearing {len(update_queues)} existing sessions")
        update_queues.clear()
    else:
        logger.info("🧹 STARTUP CLEANUP - No existing sessions to clear")

# Pydantic models for request/response
class SearchRequest(BaseModel):
    exam_id: int
    num_years: int = 5
    language: Optional[str] = None
    session_id: Optional[str] = None

class SearchResponse(BaseModel):
    session_id: str
    status: str
    message: str

@api_router.get("/updates/{session_id}")
async def get_updates(session_id: str):
    """Server-sent events endpoint for real-time updates"""
    def generate():
        # Only replace the current session if it exists, don't clear all sessions
        if session_id in update_queues:
            logger.info(f"🔄 REPLACING EXISTING SESSION - Session: {session_id}")
        else:
            logger.info(f"🆕 CREATING NEW SESSION - Session: {session_id}")

        # Create/replace queue for this specific session
        update_queues[session_id] = queue.Queue()
        q = update_queues[session_id]
        logger.info(f"✅ SESSION QUEUE READY - Session: {session_id}")
        
        # Send initial message
        yield 'data: {"status": "connected"}\n\n'
        
        while True:
            try:
                # Get message from queue without timeout - wait indefinitely for AI verification
                message = q.get(block=True)
                yield f'data: {json.dumps(message)}\n\n'

                # Check if this is a completion message to close the connection
                if message.get('status') in ['complete', 'critical_error']:
                    break
            except Exception as e:
                # Log any unexpected errors but don't break the connection
                logger.error(f"SSE error for session {session_id}: {e}")
                yield f'data: {{"status": "error", "message": "Connection error occurred"}}\n\n'

        # Clean up the session when connection closes
        if session_id in update_queues:
            del update_queues[session_id]
            logger.info(f"🧹 CLEANED UP SESSION - Session: {session_id}")
    
    return StreamingResponse(generate(), media_type='text/event-stream')

@api_router.post("/search", response_model=SearchResponse)
async def search_papers(
    search_request: SearchRequest, 
    background_tasks: BackgroundTasks,
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """Start the paper search process"""
    if login_check:
        return login_check

    exam_id = search_request.exam_id
    num_years = search_request.num_years
    language = search_request.language
    session_id = search_request.session_id or str(uuid.uuid4())

    logger.info(f"🚀 STARTING SEARCH - Session: {session_id}")

    if not isinstance(num_years, int) or num_years < 1 or num_years > 30:
        raise HTTPException(status_code=400, detail="Number of years must be between 1 and 30")

    # Get exam details from database
    exam = get_exam_by_id(exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Note: Queue will be created by the SSE endpoint when client connects
    
    # Get username from session
    username = request.session.get('username', 'system')
    
    # Start a background task to process the search
    background_tasks.add_task(
        process_search,
        session_id,
        exam_id,
        exam["exam_name"],
        exam["subject"],
        num_years,
        language,
        username
    )

    return SearchResponse(
        session_id=session_id,
        status="processing",
        message=f"Started searching for {exam['exam_name']} {exam['subject']} papers"
    )

def process_search(session_id: str, exam_id: int, exam_name: str, subject: str, 
                  num_years: int, language: Optional[str], username: str):
    """Process the search using the service layer - clean separation of concerns"""
    try:
        # Create notification service for this session
        notification_service = NotificationService(update_queues)

        # Create search service with notification service
        search_service = SearchService(notification_service)

        # Execute the search - all business logic is in the service layer
        search_service.search_papers(session_id, exam_id, exam_name, subject, num_years, language, username)

    except Exception as e:
        logger.error(f"❌ CRITICAL ERROR in process_search - Session: {session_id} - Error: {e}")
        # Send error notification if possible
        if session_id in update_queues:
            update_queues[session_id].put({
                'status': 'critical_error',
                'message': f"Critical error occurred: {str(e)}"
            })

@api_router.get("/exam/{exam_id}/details")
async def get_exam_details(
    exam_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """Get exam details for search form population"""
    if login_check:
        return login_check

    exam = get_exam_by_id(exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    return {
        "id": exam["id"],
        "exam_name": exam["exam_name"],
        "level": exam["level"],
        "syllabus": exam["syllabus"],
        "grade": exam["grade"],
        "subject": exam["subject"]
    }
