document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('searchForm');
    const searchButton = document.getElementById('searchButton');
    const searchSpinner = document.getElementById('searchSpinner');
    const searchIcon = document.getElementById('searchIcon');
    const progressContainer = document.getElementById('progressContainer');
    const progressLog = document.getElementById('progressLog');

    // Set current year as default for Starting Year
    const startingYearInput = document.getElementById('startingYear');
    if (startingYearInput) {
        startingYearInput.value = new Date().getFullYear();
    }

    // Global variables for SSE
    let eventSource = null;
    let sessionId = null;

    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form values
        const examId = document.getElementById('examId').value;
        const startingYear = document.getElementById('startingYear').value.trim();
        const numYears = document.getElementById('numYears').value.trim();
        const language = document.getElementById('language').value.trim();

        if (!examId || !startingYear || !numYears) {
            alert('Please enter both starting year and number of years to search');
            return;
        }

        // Validate starting year
        const startingYearInt = parseInt(startingYear);
        if (isNaN(startingYearInt) || startingYearInt < 1990 || startingYearInt > 2030) {
            alert('Starting year must be between 1990 and 2030');
            return;
        }

        // Validate number of years
        const numYearsInt = parseInt(numYears);
        if (isNaN(numYearsInt) || numYearsInt < 1 || numYearsInt > 30) {
            alert('Number of years must be between 1 and 30');
            return;
        }

        // Validate that starting year minus number of years doesn't go below 1990
        const endYear = startingYearInt - numYearsInt + 1;
        if (endYear < 1990) {
            alert(`With starting year ${startingYearInt} and ${numYearsInt} years, the search would go back to ${endYear}. Please adjust to not go below 1990.`);
            return;
        }

        // Close any existing event source
        if (eventSource) {
            eventSource.close();
            eventSource = null;
        }

        // Generate a new session ID
        sessionId = generateUUID();

        // Show loading spinner and disable button
        searchButton.disabled = true;
        searchSpinner.style.display = 'inline-block';
        searchIcon.style.display = 'none';

        // Update button text
        const buttonText = document.getElementById('buttonText');
        if (buttonText) {
            buttonText.textContent = 'Searching...';
        }

        // Clear progress log (remove default message)
        const defaultMessage = document.getElementById('defaultMessage');
        if (defaultMessage) {
            defaultMessage.remove();
        }

        // Start SSE connection
        startEventSource(sessionId);

        // Start the search
        fetch('/question-paper-aggregator/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                exam_id: parseInt(examId),
                starting_year: startingYearInt,
                num_years: numYearsInt,
                language: language || null,
                session_id: sessionId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'processing') {
                addLogEntry('🚀 Search started successfully', 'status-started');
            } else {
                addLogEntry('❌ Failed to start search', 'status-error');
                resetSearchButton();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            addLogEntry('❌ Error starting search: ' + error.message, 'status-error');
            resetSearchButton();
        });
    });

    function startEventSource(sessionId) {
        console.log('Starting SSE connection for session:', sessionId);
        eventSource = new EventSource(`/question-paper-aggregator/updates/${sessionId}`);

        eventSource.onopen = function(event) {
            console.log('SSE connection opened');
            addLogEntry('🔗 Connected to search service', 'status-processing');
        };

        eventSource.onmessage = function(event) {
            console.log('SSE message received:', event.data);
            try {
                const data = JSON.parse(event.data);
                handleUpdate(data);
            } catch (error) {
                console.error('Error parsing SSE data:', error, 'Raw data:', event.data);
                addLogEntry('❌ Error parsing server message', 'status-error');
            }
        };

        eventSource.onerror = function(error) {
            console.error('SSE error:', error);
            addLogEntry('❌ Connection error occurred', 'status-error');
        };
    }

    function handleUpdate(data) {
        console.log('Handling update:', data);
        const status = data.status;
        const message = data.message;

        switch (status) {
            case 'connected':
                // Already handled in onopen
                break;
            case 'started':
                addLogEntry(`📋 ${message}`, 'status-started');
                break;
            case 'processing_year':
                addLogEntry(`📅 Processing year ${data.year}`, 'status-processing');
                break;
            case 'searching_subject':
                addLogEntry(`🔍 ${message}`, 'status-processing');
                break;
            case 'found_links':
                addLogEntry(`📄 Found ${data.count} potential papers for ${data.subject} (${data.year})`, 'status-processing');
                break;
            case 'downloading':
                addLogEntry(`📥 Downloading paper ${data.index}/${data.total} for ${data.subject} (${data.year})`, 'status-processing');
                break;
            case 'verifying':
                addLogEntry(`🤖 Verifying paper for ${data.subject} (${data.year})...`, 'status-processing');
                break;
            case 'verified':
                addLogEntry(`✅ Verified paper found for ${data.subject} (${data.year})`, 'status-verified');
                break;
            case 'upload_started':
                addLogEntry(`📤 Uploading verified paper for year ${data.year}`, 'status-processing');
                break;
            case 'upload_success':
                addLogEntry(`🎉 Successfully uploaded paper for year ${data.year}`, 'status-uploaded');
                break;
            case 'not_found':
                addLogEntry(`❌ No papers found for ${data.subject} (${data.year})`, 'status-error');
                break;
            case 'not_verified':
                addLogEntry(`⚠️ Paper verification failed for ${data.subject} (${data.year})`, 'status-error');
                break;
            case 'fallback_search':
                addLogEntry(`🔄 ${message}`, 'status-processing');
                break;
            case 'fallback_verified':
                addLogEntry(`✅ Verified fallback paper found for ${data.subject}`, 'status-verified');
                break;
            case 'complete':
                console.log('Search completed with results:', data.results, 'count:', data.count);
                addLogEntry(`🏁 Search completed! Found ${data.count} papers.`, 'status-complete');

                // Transform the card to show completion summary
                setTimeout(() => {
                    transformToCompletionSummary(data.results, data.count);
                }, 1000); // Small delay to let users see the completion message

                resetSearchButton();
                if (eventSource) {
                    eventSource.close();
                    eventSource = null;
                }
                break;
            case 'critical_error':
                addLogEntry(`💥 Critical error: ${message}`, 'status-error');
                resetSearchButton();
                if (eventSource) {
                    eventSource.close();
                    eventSource = null;
                }
                break;
            default:
                console.log('Unhandled status:', status, 'message:', message);
                if (message) {
                    addLogEntry(`ℹ️ ${message}`, 'status-processing');
                }
        }

        // Auto-scroll to bottom of log
        progressLog.scrollTop = progressLog.scrollHeight;
    }

    function addLogEntry(message, className = '') {
        console.log('Adding log entry:', message, 'className:', className);
        const entry = document.createElement('div');
        entry.className = `log-entry ${className}`;
        entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        progressLog.appendChild(entry);
        console.log('Log entry added. Total entries:', progressLog.children.length);

        // Auto-scroll to bottom
        progressLog.scrollTop = progressLog.scrollHeight;
    }



    function resetSearchButton() {
        console.log('Resetting search button');
        searchButton.disabled = false;
        searchSpinner.style.display = 'none';
        searchIcon.style.display = 'inline-block';

        // Reset button text
        const buttonText = document.getElementById('buttonText');
        if (buttonText) {
            buttonText.textContent = 'Start Intelligent Search';
        }
    }

    function transformToCompletionSummary(results, count) {
        console.log('Transforming card to completion summary');

        // Count successful uploads
        const uploadedCount = results.filter(r => r.status === 'uploaded').length;
        const foundButNotUploadedCount = results.filter(r => r.status === 'found_not_uploaded').length;
        const notFoundCount = results.filter(r => r.status === 'not_verified' || r.status === 'not_found').length;

        // Update the card title
        const progressTitle = document.getElementById('progressTitle');
        progressTitle.textContent = uploadedCount > 0 ? '🎉 Search Complete - Success!' : '⚠️ Search Complete - No Papers Found';
        progressTitle.style.color = uploadedCount > 0 ? '#059669' : '#dc2626';

        // Create completion summary content
        const progressContent = document.getElementById('progressContent');

        // Create summary content
        let summaryHTML = '<div class="completion-summary-content">';

        // Results breakdown
        summaryHTML += '<div class="results-breakdown">';
        summaryHTML += `<div class="result-stat ${uploadedCount > 0 ? 'success' : 'neutral'}">`;
        summaryHTML += `<span class="stat-number">${uploadedCount}</span>`;
        summaryHTML += `<span class="stat-label">Papers Uploaded</span>`;
        summaryHTML += '</div>';

        if (foundButNotUploadedCount > 0) {
            summaryHTML += `<div class="result-stat warning">`;
            summaryHTML += `<span class="stat-number">${foundButNotUploadedCount}</span>`;
            summaryHTML += `<span class="stat-label">Found but Failed Upload</span>`;
            summaryHTML += '</div>';
        }

        if (notFoundCount > 0) {
            summaryHTML += `<div class="result-stat neutral">`;
            summaryHTML += `<span class="stat-number">${notFoundCount}</span>`;
            summaryHTML += `<span class="stat-label">Years with No Papers</span>`;
            summaryHTML += '</div>';
        }
        summaryHTML += '</div>';

        // Summary message
        summaryHTML += '<div class="summary-message">';
        if (uploadedCount > 0) {
            summaryHTML += `<p><strong>Great success!</strong> Found and uploaded ${uploadedCount} question paper${uploadedCount > 1 ? 's' : ''} to your database.</p>`;
            if (foundButNotUploadedCount > 0 || notFoundCount > 0) {
                summaryHTML += `<p>Some years had no suitable papers found or verification issues, but the successful uploads are now available in your Question Papers section.</p>`;
            }
        } else {
            summaryHTML += '<p><strong>No papers were uploaded.</strong> This could be due to:</p>';
            summaryHTML += '<ul>';
            summaryHTML += '<li>No relevant papers found in search results</li>';
            summaryHTML += '<li>Papers found but failed AI verification</li>';
            summaryHTML += '<li>Papers verified but failed to upload</li>';
            summaryHTML += '</ul>';
            summaryHTML += '<p>Try adjusting search parameters or upload papers manually.</p>';
        }
        summaryHTML += '</div>';

        // Action buttons
        summaryHTML += '<div class="completion-actions">';
        summaryHTML += `<a href="/pyqs_admin/exams/${document.getElementById('examId').value}/documents" class="completion-btn primary">`;
        summaryHTML += '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">';
        summaryHTML += '<path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>';
        summaryHTML += '</svg>';
        summaryHTML += 'Back to Question Papers';
        summaryHTML += '</a>';

        if (uploadedCount === 0) {
            summaryHTML += '<button class="completion-btn secondary" onclick="resetForNewSearch()">';
            summaryHTML += '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">';
            summaryHTML += '<path fill-rule="evenodd" d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>';
            summaryHTML += '<path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>';
            summaryHTML += '</svg>';
            summaryHTML += 'Try Different Parameters';
            summaryHTML += '</button>';
        }
        summaryHTML += '</div>';
        summaryHTML += '</div>';

        // Replace the progress content with summary
        progressContent.innerHTML = summaryHTML;
    }

    // Global function for reset button
    window.resetForNewSearch = function() {
        const progressTitle = document.getElementById('progressTitle');
        const progressContent = document.getElementById('progressContent');

        progressTitle.textContent = 'Search Progress';
        progressTitle.style.color = '';

        progressContent.innerHTML = `
            <div class="progress-log" id="progressLog">
                <div class="log-entry" style="color: #6b7280; font-style: italic;">
                    Ready to start another search. Adjust parameters above and click "Start Intelligent Search".
                </div>
            </div>
        `;
    }

    function generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
});
