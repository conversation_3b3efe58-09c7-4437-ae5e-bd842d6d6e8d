"""
Service for fixing issues in questions using LLM.
"""

import json
import logging
import asyncio
import uuid
from typing import Optional, Dict, Any, List
from contextlib import contextmanager

import config
from agents.schemas.agent_prompts import fix_formula_prompt, fix_json_prompt
from services.request_tracker_service import RequestTrackerService
from llm_manager.llm_factory import LLMFactory
from config import llm_config
from langchain_core.messages import HumanMessage
from db_config.db import get_engine, CONTENT_SCHEMA

# Get logger instance
logger = logging.getLogger(__name__)

# Initialize LLM factory
llm_factory = LLMFactory(llm_config)


class QuestionFixService:
    """
    Service for fixing issues in questions using LLM.
    """

    def __init__(self, max_concurrent_fixes=None):
        """
        Initialize the QuestionFixService.
        """
        self.max_concurrent_fixes = max_concurrent_fixes or getattr(config, 'MAX_CONCURRENT_EXTRACTIONS', 2)
        
        # Delay LLM initialization until first use
        self.llm = None
        self._llm_initialized = False
        
        # Initialize request tracker
        self.tracker_service = RequestTrackerService()

    @contextmanager
    def get_connection(self):
        """
        Context manager to get a direct database connection.
        This ensures the connection is properly closed after use.
        """
        # Get the engine
        engine = get_engine(CONTENT_SCHEMA)

        # Create a new connection
        connection = engine.raw_connection()
        try:
            cursor = connection.cursor()
            yield cursor
            connection.commit()
            logger.info("Transaction committed successfully")
        except Exception as e:
            connection.rollback()
            logger.error("Transaction rolled back due to error: " + str(e))
            raise e
        finally:
            cursor.close()
            connection.close()
            logger.info("Database connection closed")

    def _initialize_llm(self):
        """Initialize LLM if not already initialized."""
        if not self._llm_initialized:
            self.llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini", req_timeout=180)
            self._llm_initialized = True

    def _validate_json(self, json_content: str, task_id: str) -> bool:
        """
        Validate if the given content is valid JSON.

        Args:
            json_content: JSON string to validate
            task_id: Task ID for logging

        Returns:
            bool: True if valid JSON, False otherwise
        """
        try:
            json.loads(json_content)
            return True
        except json.JSONDecodeError as e:
            logger.warning(f"[TASK:{task_id}] JSON validation failed: {e}")
            return False
        except Exception as e:
            logger.warning(f"[TASK:{task_id}] JSON validation error: {e}")
            return False

    async def _fix_json_with_llm(self, invalid_json: str, task_id: str) -> str:
        """
        Use LLM to fix invalid JSON content.

        Args:
            invalid_json: Invalid JSON string to fix
            task_id: Task ID for logging

        Returns:
            str: Fixed JSON string, or None if failed
        """
        try:
            # Initialize LLM if needed
            self._initialize_llm()

            # Create the prompt using fix_json_prompt
            prompt_text = fix_json_prompt(invalid_json)

            logger.info(f"[TASK:{task_id}] Calling LLM to fix invalid JSON")

            # Call LLM
            response = await self.llm.ainvoke([HumanMessage(content=prompt_text)])

            # Extract content from response
            fixed_json_content = response.content.strip()

            logger.info(f"[TASK:{task_id}] LLM response received for JSON fix")

            return fixed_json_content

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error calling LLM to fix JSON: {e}")
            return None

    async def _validate_and_fix_json_response(self, json_content: str, question_id: int, task_id: str) -> dict:
        """
        Validate JSON content and fix it using LLM if invalid.

        Args:
            json_content: JSON string to validate and potentially fix
            question_id: Question ID for logging
            task_id: Task ID for logging

        Returns:
            dict: Valid JSON object, or None if cannot be fixed
        """
        # First, try to validate the original JSON
        if self._validate_json(json_content, task_id):
            logger.info(f"[TASK:{task_id}] Question {question_id} JSON is valid")
            try:
                return json.loads(json_content)
            except json.JSONDecodeError:
                # This shouldn't happen since we just validated it, but just in case
                logger.error(f"[TASK:{task_id}] Unexpected JSON parse error after validation")
                return None

        logger.warning(f"[TASK:{task_id}] Question {question_id} JSON is invalid, attempting to fix with LLM")

        # Try to fix the JSON using LLM
        fixed_json = await self._fix_json_with_llm(json_content, task_id)

        if fixed_json is None:
            logger.error(f"[TASK:{task_id}] Failed to get fixed JSON from LLM for question {question_id}")
            return None

        # Validate the fixed JSON
        if self._validate_json(fixed_json, task_id):
            logger.info(f"[TASK:{task_id}] Question {question_id} JSON successfully fixed by LLM")
            try:
                return json.loads(fixed_json)
            except json.JSONDecodeError:
                logger.error(f"[TASK:{task_id}] Unexpected JSON parse error after LLM fix")
                return None
        else:
            logger.error(f"[TASK:{task_id}] LLM-fixed JSON for question {question_id} is still invalid, skipping question")
            return None

    def get_question_details(self, question_id: int) -> Optional[Dict[str, Any]]:
        """
        Get question details from database with directions from directions_mst table.

        Args:
            question_id: Question ID

        Returns:
            dict: Question details or None if not found
        """
        try:
            with self.get_connection() as cursor:
                query = """
                    SELECT s.id, s.question, s.option1, s.option2, s.option3, s.option4, s.option5,
                           s.solution, s.answer, s.topic, s.subtopic, s.difficulty_level,
                           s.marks, s.negative_mark, s.question_type, s.direction_id,
                           d.directions
                    FROM wscontent.content_exam_solutions s
                    LEFT JOIN wscontent.directions_mst d ON s.direction_id = d.id
                    WHERE s.id = %s
                """
                cursor.execute(query, (question_id,))
                result = cursor.fetchone()

                if result:
                    columns = [desc[0] for desc in cursor.description]
                    question_data = dict(zip(columns, result))

                    # Add directions field for backward compatibility
                    question_data['directions'] = question_data.get('directions', '')

                    return question_data
                return None

        except Exception as e:
            logger.error(f"Error getting question details for ID {question_id}: {e}")
            return None

    def update_question_details(self, question_id: int, updated_data: Dict[str, Any]) -> bool:
        """
        Update question details in database. Handles directions separately.

        Args:
            question_id: Question ID
            updated_data: Dictionary with updated fields

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with self.get_connection() as cursor:
                # Handle directions separately if present
                direction_id = None
                directions_text = updated_data.get('directions')

                if directions_text and directions_text.strip():
                    # Create or update direction
                    direction_id = self._create_or_get_direction(cursor, directions_text)
                    if direction_id is None:
                        logger.error(f"Failed to create/get direction for question {question_id}")
                        return False

                # Build dynamic update query for content_exam_solutions table
                update_fields = []
                values = []

                for field, value in updated_data.items():
                    if field in ['question', 'option1', 'option2', 'option3', 'option4', 'option5',
                                'solution', 'answer', 'topic', 'subtopic', 'difficulty_level']:
                        update_fields.append(f"{field} = %s")
                        values.append(value)

                # Add direction_id if we have one
                if direction_id is not None:
                    update_fields.append("direction_id = %s")
                    values.append(direction_id)

                if not update_fields:
                    logger.warning(f"No valid fields to update for question {question_id}")
                    return False

                values.append(question_id)

                query = f"""
                    UPDATE wscontent.content_exam_solutions
                    SET {', '.join(update_fields)}
                    WHERE id = %s
                """

                cursor.execute(query, values)

                if cursor.rowcount > 0:
                    logger.info(f"Successfully updated question {question_id}")
                    return True
                else:
                    logger.warning(f"No rows updated for question {question_id}")
                    return False

        except Exception as e:
            logger.error(f"Error updating question {question_id}: {e}")
            return False

    def _create_or_get_direction(self, cursor, directions_text: str) -> Optional[int]:
        """
        Create a new direction or get existing one with the same text.

        Args:
            cursor: Database cursor
            directions_text: Direction text

        Returns:
            int: Direction ID or None if failed
        """
        try:
            # First, check if a direction with this text already exists
            check_query = """
                SELECT id FROM wscontent.directions_mst
                WHERE directions = %s
                LIMIT 1
            """
            cursor.execute(check_query, (directions_text,))
            existing = cursor.fetchone()

            if existing:
                return existing[0]

            # Create new direction
            insert_query = """
                INSERT INTO wscontent.directions_mst (version, directions, no_of_questions, passage)
                VALUES (0, %s, NULL, FALSE)
            """
            cursor.execute(insert_query, (directions_text,))

            # Get the ID of the newly created direction
            cursor.execute("SELECT LAST_INSERT_ID() as id")
            last_id = cursor.fetchone()[0]

            return last_id

        except Exception as e:
            logger.error(f"Error creating/getting direction: {e}")
            return None

    async def fix_questions_issues(self, question_ids: List[int], issue_types: List[str], username=None):
        """
        Fix issues for multiple questions.

        Args:
            question_ids: List of question IDs to fix
            issue_types: List of issue types to fix (e.g., ['fix_formulas'])
            username: Username for tracking

        Returns:
            dict: Result with task_id for tracking
        """
        # Create a task for tracking
        task_id = self.tracker_service.create_task("question_fix")
        
        if not task_id:
            return {"status": "error", "message": "Failed to create tracking task"}

        # Start the fix process in background
        asyncio.create_task(self._fix_questions_background(
            task_id, question_ids, issue_types, username
        ))
        
        return {"status": "success", "task_id": task_id}

    async def _fix_questions_background(self, task_id: str, question_ids: List[int], issue_types: List[str], username=None):
        """
        Background task for fixing question issues.
        """
        try:
            logger.info(f"[TASK:{task_id}] Starting question fix for {len(question_ids)} questions with issues: {issue_types}")

            # Update task status to in progress
            self.tracker_service.update_task_status(task_id, "IN_PROGRESS")

            # Process questions with parallel processing
            await self._process_questions_fixes(task_id, question_ids, issue_types)
            
            # Mark task as completed
            self.tracker_service.update_task_status(task_id, "COMPLETED")
            logger.info(f"[TASK:{task_id}] Question fix completed successfully")
            
        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error in question fix: {e}")
            self.tracker_service.update_task_status(task_id, "FAILED", str(e))

    async def _process_questions_fixes(self, task_id: str, question_ids: List[int], issue_types: List[str]):
        """Process fixes for all questions using parallel processing."""
        try:
            # Initialize LLM if needed
            self._initialize_llm()

            # Create semaphore for controlling concurrency
            semaphore = asyncio.Semaphore(self.max_concurrent_fixes)

            # Create tasks for parallel processing
            tasks = []
            for i, question_id in enumerate(question_ids):
                task = self._fix_single_question(
                    semaphore, task_id, question_id, issue_types, i + 1, len(question_ids)
                )
                tasks.append(task)

            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Check for any failures
            failed_count = sum(1 for result in results if isinstance(result, Exception))
            success_count = len(results) - failed_count

            logger.info(f"[TASK:{task_id}] Question fix completed: {success_count} successful, {failed_count} failed")

            if failed_count > 0:
                logger.warning(f"[TASK:{task_id}] {failed_count} questions failed to fix")

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error in question fix processing: {e}")
            raise

    async def _fix_single_question(self, semaphore, task_id: str, question_id: int, issue_types: List[str], current_num: int, total_num: int):
        """Fix issues for a single question."""
        async with semaphore:
            try:
                logger.info(f"[TASK:{task_id}] Processing question {current_num}/{total_num} (ID: {question_id})")

                # Get question details from database
                question_details = self.get_question_details(question_id)
                if not question_details:
                    logger.error(f"[TASK:{task_id}] Question {question_id} not found in database")
                    return None

                # Process each issue type
                for issue_type in issue_types:
                    if issue_type == 'fix_formulas':
                        await self._fix_formulas_for_question(task_id, question_id, question_details, current_num, total_num)
                    else:
                        logger.warning(f"[TASK:{task_id}] Unknown issue type: {issue_type}")

                return True

            except Exception as e:
                logger.error(f"[TASK:{task_id}] Failed to fix question {question_id}: {e}")
                raise

    async def _fix_formulas_for_question(self, task_id: str, question_id: int, question_details: Dict[str, Any], current_num: int, total_num: int):
        """Fix formulas for a single question using LLM."""
        try:
            # Format question text for LLM
            formatted_question = self._format_question_for_fix(question_details)

            # Create prompt using fix_formula_prompt
            prompt_text = fix_formula_prompt(formatted_question)

            logger.info(f"[TASK:{task_id}] Calling LLM to fix formulas for question {current_num}/{total_num} (ID: {question_id})")

            # Call LLM
            response = await self.llm.ainvoke([HumanMessage(content=prompt_text)])
            print(response.content)
            response_content = response.content.strip()

            # Validate and fix JSON response if needed
            fixed_json = await self._validate_and_fix_json_response(response_content, question_id, task_id)

            if fixed_json is None:
                logger.error(f"[TASK:{task_id}] Failed to parse JSON response for question {question_id}, skipping")
                return None

            # Extract the first question from the response (should only be one)
            questions_list = fixed_json.get("questions", [])
            if not questions_list:
                logger.error(f"[TASK:{task_id}] No questions found in LLM response for question {question_id}")
                return None

            fixed_question = questions_list[0]

            # Prepare update data
            update_data = {}

            # Update fields that might have been fixed
            if fixed_question.get("question"):
                update_data["question"] = fixed_question["question"]
            if fixed_question.get("option1"):
                update_data["option1"] = fixed_question["option1"]
            if fixed_question.get("option2"):
                update_data["option2"] = fixed_question["option2"]
            if fixed_question.get("option3"):
                update_data["option3"] = fixed_question["option3"]
            if fixed_question.get("option4"):
                update_data["option4"] = fixed_question["option4"]
            if fixed_question.get("option5"):
                update_data["option5"] = fixed_question["option5"]
            if fixed_question.get("solution"):
                update_data["solution"] = fixed_question["solution"]
            if fixed_question.get("directions"):
                update_data["directions"] = fixed_question["directions"]

            # Update database with fixed content
            if update_data:
                success = self.update_question_details(question_id, update_data)
                if success:
                    logger.info(f"[TASK:{task_id}] Formulas fixed and updated in database for question {current_num}/{total_num} (ID: {question_id})")
                else:
                    logger.warning(f"[TASK:{task_id}] Failed to update database for question {question_id}")
            else:
                logger.info(f"[TASK:{task_id}] No changes needed for question {question_id}")

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error fixing formulas for question {question_id}: {e}")
            raise

    def _format_question_for_fix(self, question_details: Dict[str, Any]) -> str:
        """Format question details for LLM processing."""
        formatted_parts = []

        # Add direction if present
        if question_details.get("directions"):
            formatted_parts.append(f"Direction - {question_details['directions']}")

        # Add question number and text
        question_text = question_details.get("question", "")
        formatted_parts.append(f"Question {question_details.get('id', '')}. {question_text}")

        # Add options
        for i in range(1, 6):  # Options 1-5
            option_key = f"option{i}"
            if question_details.get(option_key):
                formatted_parts.append(f"Option {i}")
                formatted_parts.append(question_details[option_key])

        # Add solution if present
        if question_details.get("solution"):
            formatted_parts.append(f"Solution: {question_details['solution']}")

        return "\n".join(formatted_parts)
