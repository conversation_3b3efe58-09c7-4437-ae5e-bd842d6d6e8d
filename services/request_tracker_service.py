"""
Service for handling request tracker operations using wslog.request_tracker table.
"""

import logging
import uuid
import datetime
from contextlib import contextmanager
from typing import Optional, Dict, Any

from db_config.db import get_engine, LOG_SCHEMA

# Get logger instance
logger = logging.getLogger(__name__)

class RequestTrackerService:
    """
    Service for handling request tracker operations.
    Uses wslog.request_tracker table to track long-running tasks.
    """

    @contextmanager
    def get_connection(self):
        """
        Context manager to get a direct database connection.
        This ensures the connection is properly closed after use.
        """
        # Get the engine
        engine = get_engine(LOG_SCHEMA)

        # Create a new connection
        connection = engine.raw_connection()
        try:
            cursor = connection.cursor()
            yield cursor
            connection.commit()
            logger.info("Request tracker transaction committed successfully")
        except Exception as e:
            connection.rollback()
            logger.error(f"Request tracker transaction rolled back due to error: {e}")
            raise e
        finally:
            cursor.close()
            connection.close()
            logger.info("Request tracker database connection closed")

    def create_task(self, task_type: str = "mcq_text_extraction", additional_data: Dict[str, Any] = None) -> str:
        """
        Create a new task record in request_tracker table.

        Args:
            task_type: Type of task being tracked
            additional_data: Additional data to store (will be JSON serialized)

        Returns:
            str: Generated UUID for the task (request_id)
        """
        try:
            # Generate a UUID for the task
            request_id = str(uuid.uuid4())

            # Get current time
            current_time = datetime.datetime.now()

            # Prepare additional data as JSON string if provided
            error_message = None
            error = None

            # Insert a new record
            with self.get_connection() as cursor:
                insert_query = f"""
                    INSERT INTO {LOG_SCHEMA}.request_tracker
                    (version, date_created, error, error_message, last_updated, request_id, status)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """

                # Log the query
                logger.info(f"Creating new task with request_id: {request_id}")

                # Execute the query
                cursor.execute(insert_query, (
                    0,                    # version
                    current_time,         # date_created
                    error,               # error (NULL initially)
                    error_message,       # error_message (NULL initially)
                    current_time,         # last_updated
                    request_id,          # request_id
                    'STARTED'            # status
                ))

                # Log the result
                rows_affected = cursor.rowcount
                logger.info(f"Task creation query affected {rows_affected} rows")

            return request_id
        except Exception as e:
            logger.error(f"Error creating task: {e}")
            return None

    def update_task_status(self, request_id: str, status: str, error_message: str = None, result_data: str = None) -> bool:
        """
        Update task status in request_tracker table.

        Args:
            request_id: Task ID to update
            status: New status ('STARTED', 'IN_PROGRESS', 'COMPLETED', 'FAILED')
            error_message: Error message if status is 'FAILED', or result data if status is 'COMPLETED'
            result_data: Additional result data (stored in error_message field for completed tasks)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get current time
            current_time = datetime.datetime.now()

            # Update the record
            with self.get_connection() as cursor:
                update_query = f"""
                    UPDATE {LOG_SCHEMA}.request_tracker
                    SET status = %s, last_updated = %s, error = %s, error_message = %s
                    WHERE request_id = %s
                """

                # Set error flag based on status
                error_flag = 1 if status == 'FAILED' else None

                # For completed tasks, store result data in error_message field
                # For failed tasks, store error message
                message_to_store = error_message
                if status == 'COMPLETED' and result_data:
                    import json
                    message_to_store = json.dumps(result_data) if isinstance(result_data, dict) else result_data

                # Log the query
                logger.info(f"Updating task {request_id} to status: {status}")

                # Execute the query
                cursor.execute(update_query, (
                    status,
                    current_time,
                    error_flag,
                    message_to_store,
                    request_id
                ))

                # Log the result
                rows_affected = cursor.rowcount
                logger.info(f"Task update query affected {rows_affected} rows")

                if rows_affected == 0:
                    logger.warning(f"No task found with request_id: {request_id}")
                    return False

            return True
        except Exception as e:
            logger.error(f"Error updating task status: {e}")
            return False

    def get_task_status(self, request_id: str) -> Dict[str, Any]:
        """
        Get the status of a task.

        Args:
            request_id: Task ID to check

        Returns:
            dict: Task status details
        """
        try:
            with self.get_connection() as cursor:
                query = f"""
                    SELECT date_created, error, error_message, last_updated, request_id, status
                    FROM {LOG_SCHEMA}.request_tracker
                    WHERE request_id = %s
                    LIMIT 1
                """

                # Log the query
                logger.info(f"Getting status for task: {request_id}")

                # Execute the query
                cursor.execute(query, (request_id,))

                # Fetch the result
                result = cursor.fetchone()

                if not result:
                    logger.warning(f"No task found with request_id: {request_id}")
                    return {"status": "not_found", "message": f"No task found with ID {request_id}"}

                # Extract the result
                date_created, error, error_message, last_updated, req_id, status = result

                # Format the response
                response = {
                    "request_id": req_id,
                    "status": status,
                    "date_created": date_created.isoformat() if date_created else None,
                    "last_updated": last_updated.isoformat() if last_updated else None,
                    "error": bool(error) if error is not None else False,
                    "error_message": error_message
                }

                return response
        except Exception as e:
            logger.error(f"Error getting task status: {e}")
            return {"status": "error", "message": f"Error getting task status: {str(e)}"}

    def delete_task(self, request_id: str) -> bool:
        """
        Delete a task from request_tracker table.

        Args:
            request_id: Task ID to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with self.get_connection() as cursor:
                delete_query = f"""
                    DELETE FROM {LOG_SCHEMA}.request_tracker
                    WHERE request_id = %s
                """

                # Log the query
                logger.info(f"Deleting task: {request_id}")

                # Execute the query
                cursor.execute(delete_query, (request_id,))

                # Log the result
                rows_affected = cursor.rowcount
                logger.info(f"Task deletion query affected {rows_affected} rows")

                if rows_affected == 0:
                    logger.warning(f"No task found to delete with request_id: {request_id}")
                    return False

            return True
        except Exception as e:
            logger.error(f"Error deleting task: {e}")
            return False

    def clean_old_tasks(self, max_age_hours: int = 24) -> int:
        """
        Clean up old completed/failed tasks.

        Args:
            max_age_hours: Maximum age in hours for completed tasks

        Returns:
            int: Number of tasks cleaned up
        """
        try:
            # Calculate cutoff time
            cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=max_age_hours)

            with self.get_connection() as cursor:
                delete_query = f"""
                    DELETE FROM {LOG_SCHEMA}.request_tracker
                    WHERE (status = 'COMPLETED' OR status = 'FAILED')
                    AND last_updated < %s
                """

                # Log the query
                logger.info(f"Cleaning up tasks older than {max_age_hours} hours")

                # Execute the query
                cursor.execute(delete_query, (cutoff_time,))

                # Log the result
                rows_affected = cursor.rowcount
                logger.info(f"Cleaned up {rows_affected} old tasks")

                return rows_affected
        except Exception as e:
            logger.error(f"Error cleaning up old tasks: {e}")
            return 0
