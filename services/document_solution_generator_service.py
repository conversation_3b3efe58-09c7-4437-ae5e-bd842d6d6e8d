"""
Service for generating solutions for document questions using LLM.
"""

import json
import logging
import asyncio
import uuid
from typing import Optional, Dict, Any, List
from contextlib import contextmanager

import config
from agents.syllabus_processor import SyllabusProcessor
from agents.schemas.agent_prompts import qp_solution_creator_prompt, fix_json_prompt
from agents.schemas.agent_prompts import qp_solution_creator_prompt, qp_solution_creator_dual_language_prompt, fix_json_prompt
from services.request_tracker_service import RequestTrackerService
from db_config.pyqs_admin_db import get_exam_questions_with_directions, get_exam_document_by_id, get_exam_by_id, update_exam_solution_fields, update_exam_document_extracted_content_status
from llm_manager.llm_factory import LLMFactory
from config import llm_config
from langchain_core.messages import HumanMessage
from db_config.db import get_engine, CONTENT_SCHEMA

# Get logger instance
logger = logging.getLogger(__name__)

# Initialize LLM factory
llm_factory = LLMFactory(llm_config)


class DocumentSolutionGeneratorService:
    """
    Service for generating solutions for document questions using LLM.
    """

    def __init__(self, max_concurrent_generations=None):
        """
        Initialize the DocumentSolutionGeneratorService.
        """
        self.max_concurrent_generations = max_concurrent_generations or getattr(config, 'MAX_CONCURRENT_EXTRACTIONS', 2)
        
        # Delay LLM initialization until first use
        self.llm = None
        self._llm_initialized = False
        
        # Initialize request tracker
        self.tracker_service = RequestTrackerService()

    @contextmanager
    def get_connection(self):
        """
        Context manager to get a direct database connection.
        This ensures the connection is properly closed after use.
        """
        # Get the engine
        engine = get_engine(CONTENT_SCHEMA)

        # Create a new connection
        connection = engine.raw_connection()
        try:
            cursor = connection.cursor()
            yield cursor
            connection.commit()
            logger.info("Transaction committed successfully")
        except Exception as e:
            connection.rollback()
            logger.error("Transaction rolled back due to error: " + str(e))
            raise e
        finally:
            cursor.close()
            connection.close()
            logger.info("Database connection closed")

    def _initialize_llm(self):
        """Initialize LLM if not already initialized."""
        if not self._llm_initialized:
            self.llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini", req_timeout=180)
            self._llm_initialized = True

    def _validate_json(self, json_content: str, task_id: str) -> bool:
        """
        Validate if the given content is valid JSON.

        Args:
            json_content: JSON string to validate
            task_id: Task ID for logging

        Returns:
            bool: True if valid JSON, False otherwise
        """
        try:
            json.loads(json_content)
            return True
        except json.JSONDecodeError as e:
            logger.warning(f"[TASK:{task_id}] JSON validation failed: {e}")
            return False
        except Exception as e:
            logger.warning(f"[TASK:{task_id}] JSON validation error: {e}")
            return False

    async def _fix_json_with_llm(self, invalid_json: str, task_id: str) -> str:
        """
        Use LLM to fix invalid JSON content.

        Args:
            invalid_json: Invalid JSON string to fix
            task_id: Task ID for logging

        Returns:
            str: Fixed JSON string, or None if failed
        """
        try:
            # Initialize LLM if needed
            self._initialize_llm()

            # Create the prompt using fix_json_prompt
            prompt_text = fix_json_prompt(invalid_json)

            logger.info(f"[TASK:{task_id}] Calling LLM to fix invalid JSON")

            # Call LLM
            response = await self.llm.ainvoke([HumanMessage(content=prompt_text)])

            # Extract content from response
            fixed_json_content = response.content.strip()

            logger.info(f"[TASK:{task_id}] LLM response received for JSON fix")

            return fixed_json_content

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error calling LLM to fix JSON: {e}")
            return None

    async def _validate_and_fix_json_response(self, json_content: str, question_id: int, task_id: str) -> dict:
        """
        Validate JSON content and fix it using LLM if invalid.

        Args:
            json_content: JSON string to validate and potentially fix
            question_id: Question ID for logging
            task_id: Task ID for logging

        Returns:
            dict: Valid JSON object, or None if cannot be fixed
        """
        # First, try to validate the original JSON
        if self._validate_json(json_content, task_id):
            logger.info(f"[TASK:{task_id}] Question {question_id} JSON is valid")
            try:
                return json.loads(json_content)
            except json.JSONDecodeError:
                # This shouldn't happen since we just validated it, but just in case
                logger.error(f"[TASK:{task_id}] Unexpected JSON parse error after validation")
                return None

        logger.warning(f"[TASK:{task_id}] Question {question_id} JSON is invalid, attempting to fix with LLM")

        # Try to fix the JSON using LLM
        fixed_json = await self._fix_json_with_llm(json_content, task_id)

        if fixed_json is None:
            logger.error(f"[TASK:{task_id}] Failed to get fixed JSON from LLM for question {question_id}")
            return None

        # Validate the fixed JSON
        if self._validate_json(fixed_json, task_id):
            logger.info(f"[TASK:{task_id}] Question {question_id} JSON successfully fixed by LLM")
            try:
                return json.loads(fixed_json)
            except json.JSONDecodeError:
                logger.error(f"[TASK:{task_id}] Unexpected JSON parse error after LLM fix")
                return None
        else:
            logger.error(f"[TASK:{task_id}] LLM-fixed JSON for question {question_id} is still invalid, skipping question")
            return None

    async def generate_solutions_for_document(self, document_id, username=None, dual_language=False):
        """
        Generate solutions for all questions in a document.
        """
        # Create a task for tracking
        task_id = self.tracker_service.create_task("document_solution_generation")
        
        if not task_id:
            return {"status": "error", "message": "Failed to create tracking task"}

        # Start the solution generation process in background
        asyncio.create_task(self._generate_solutions_background(
            task_id, document_id, username, dual_language
        ))
        
        return {"status": "success", "task_id": task_id}

    async def _generate_solutions_background(self, task_id, document_id, username=None, dual_language=False):
        """
        Background task for generating solutions for document questions.
        """
        try:
            logger.info(f"[TASK:{task_id}] Starting solution generation for document ID {document_id}")

            # Update task status to in progress
            self.tracker_service.update_task_status(task_id, "IN_PROGRESS")

            # Step 1: Get document details
            logger.info(f"[TASK:{task_id}] Step 1: Getting document details")
            document = get_exam_document_by_id(document_id)
            if not document:
                raise Exception(f"Document with ID {document_id} not found")

            exam_id = document.get("content_exam_mst_id")
            if not exam_id:
                raise Exception(f"No exam ID found for document {document_id}")

            # Get language1 and language2 from document for dual language functionality
            language1 = document.get("language1", "")
            language2 = document.get("language2", "")

            if dual_language:
                logger.info(f"[TASK:{task_id}] Dual language mode enabled. Language1: {language1}, Language2: {language2}")

            # Step 2: Get exam details
            logger.info(f"[TASK:{task_id}] Step 2: Getting exam details")
            exam = get_exam_by_id(exam_id)
            if not exam:
                raise Exception(f"Exam with ID {exam_id} not found")

            subject = exam.get("subject")
            if not subject:
                raise Exception(f"No subject found for exam {exam_id}")

            # Step 3: Get syllabus JSON
            logger.info(f"[TASK:{task_id}] Step 3: Getting syllabus JSON")
            processor = SyllabusProcessor()
            syllabus_result = processor.get_syllabus_json(exam_id)

            if syllabus_result["status"] != "success":
                raise Exception(f"Failed to get syllabus JSON: {syllabus_result['message']}")

            syllabus_json = syllabus_result["data"]["syllabus_json"]

            # Step 4: Get questions with directions
            logger.info(f"[TASK:{task_id}] Step 4: Getting questions with directions")
            questions = get_exam_questions_with_directions(document_id)

            if not questions:
                raise Exception(f"No questions found for document {document_id}")

            logger.info(f"[TASK:{task_id}] Found {len(questions)} questions to process")

            # Step 5: Generate solutions for each question and update database
            logger.info(f"[TASK:{task_id}] Step 5: Generating solutions and updating database")
            await self._generate_solutions_for_questions(
                task_id, questions, subject, syllabus_json, dual_language, language1, language2
            )
            
            # Mark task as completed
            self.tracker_service.update_task_status(task_id, "COMPLETED")

            # Update document extracted_content status to indicate questions and solutions have been extracted
            try:
                update_success = update_exam_document_extracted_content_status(document_id, "EXTRACTED_QUES_AND_SOL")
                if update_success:
                    logger.info(f"[TASK:{task_id}] Updated document {document_id} status to EXTRACTED_QUES_AND_SOL")
                else:
                    logger.warning(f"[TASK:{task_id}] Failed to update document {document_id} status")
            except Exception as status_update_error:
                logger.error(f"[TASK:{task_id}] Error updating document status: {status_update_error}")

            logger.info(f"[TASK:{task_id}] Solution generation completed successfully")
            
        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error in solution generation: {e}")
            self.tracker_service.update_task_status(task_id, "FAILED", str(e))

    async def _generate_solutions_for_questions(self, task_id, questions, subject, syllabus_json, dual_language=False, language1=None, language2=None):
        """Generate solutions for all questions using LLM and update database."""
        try:
            # Initialize LLM if needed
            self._initialize_llm()

            # Create semaphore for controlling concurrency
            semaphore = asyncio.Semaphore(self.max_concurrent_generations)

            # Create tasks for parallel processing
            tasks = []
            for i, question in enumerate(questions):
                task = self._generate_single_solution(
                    semaphore, task_id, question, subject, syllabus_json, i + 1, len(questions), dual_language, language1, language2
                )
                tasks.append(task)

            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Check for any failures
            failed_count = sum(1 for result in results if isinstance(result, Exception))
            success_count = len(results) - failed_count

            logger.info(f"[TASK:{task_id}] Solution generation completed: {success_count} successful, {failed_count} failed")

            if failed_count > 0:
                logger.warning(f"[TASK:{task_id}] {failed_count} solutions failed to generate")

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error in solution generation: {e}")
            raise

    async def _generate_single_solution(self, semaphore, task_id, question, subject, syllabus_json, current_num, total_num, dual_language=False, language1=None, language2=None):
        """Generate solution for a single question and update database."""
        async with semaphore:
            try:
                question_id = question.get("id")
                question_number = current_num

                logger.info(f"[TASK:{task_id}] Processing question {current_num}/{total_num} (ID: {question_id})")

                # Format question text
                formatted_question = self._format_question_text(question)

                # Create prompt using appropriate prompt based on dual_language flag
                if dual_language:
                    prompt_text = qp_solution_creator_dual_language_prompt(subject, syllabus_json, formatted_question, language1, language2)
                    logger.info(f"[TASK:{task_id}] Using dual language prompt for question {current_num} (ID: {question_id}) with languages: {language1}, {language2}")
                else:
                    prompt_text = qp_solution_creator_prompt(subject, syllabus_json, formatted_question, language1)
                    logger.info(f"[TASK:{task_id}] Using single language prompt for question {current_num} (ID: {question_id})")
                
                # Call LLM
                response = await self.llm.ainvoke([HumanMessage(content=prompt_text)])
                response_content = response.content.strip()

                # Validate and fix JSON response if needed
                solution_json = await self._validate_and_fix_json_response(response_content, question_id, task_id)

                if solution_json is None:
                    logger.error(f"[TASK:{task_id}] Failed to parse JSON response for question {question_id}, skipping")
                    return None

                # Extract fields from LLM response
                answer = solution_json.get("correctAnswerOption")
                topic = solution_json.get("topic")
                subtopic = solution_json.get("subtopic")
                difficulty_level = solution_json.get("difficultyLevel")
                solution_text = solution_json.get("solution")

                # Update database with solution fields
                success = update_exam_solution_fields(
                    solution_id=question_id,
                    answer=answer,
                    topic=topic,
                    subtopic=subtopic,
                    difficulty_level=difficulty_level,
                    solution=solution_text
                )

                if success:
                    logger.info(f"[TASK:{task_id}] Solution updated in database for question {current_num}/{total_num} (ID: {question_id})")
                else:
                    logger.warning(f"[TASK:{task_id}] Failed to update database for question {question_id}")

                return solution_json
                        
            except Exception as e:
                logger.error(f"[TASK:{task_id}] Failed to generate solution for question {question_id}: {e}")
                raise

    def _format_question_text(self, question):
        """Format question text according to the specified format."""
        formatted_parts = []
        
        # Add direction if present
        if question.get("direction") and question["direction"].get("directions"):
            formatted_parts.append(f"Direction - {question['direction']['directions']}")
        
        # Add question number and text
        question_text = question.get("question", "")
        formatted_parts.append(f"Question {question.get('id', '')}. {question_text}")
        
        # Add options
        for i in range(1, 6):  # Options 1-5
            option_key = f"option{i}"
            if question.get(option_key):
                formatted_parts.append(f"Option {i}")
                formatted_parts.append(question[option_key])
        
        return "\n".join(formatted_parts)
