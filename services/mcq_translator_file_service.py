import os
import json
import logging
import asyncio
import traceback

from typing import Dict, List, Optional, Any, Union
import base64
import uuid
import time
import requests

import tempfile

import config
from agents import mcq_extractor
from agents.utils.pdf_helpers import PDFImageConverter
from agents.core.extractor import ExtractorAgent
from agents.mcq_extractor import MCQExtractor
from agents.schemas.agent_prompts import mcq_text_extractor_prompt, mcq_parser_prompt, translator_prompt
from services.mcq_text_extractor_service import MCQTextExtractorService
from utils.s3_utils import upload_file_to_s3, read_file_from_s3, get_s3_path, run_sudo_command
from llm_manager.llm_factory import LLMFactory
from langchain_core.messages import HumanMessage
from config import llm_config


# Initialize logger
logger = logging.getLogger(__name__)

# Get LLM factory
llm_factory = LLMFactory(llm_config)


class MCQTranslatorFileService:
    """Service for translating MCQ content from uploaded PDF files."""

    def __init__(self, max_concurrent_translations: int = None):
        """
        Initialize the MCQTranslatorFileService.

        Args:
            max_concurrent_translations: Maximum number of concurrent translations to run in parallel
        """
        self.max_concurrent_translations = max_concurrent_translations or getattr(config, 'MAX_CONCURRENT_EXTRACTIONS', 2)
        self.pdf_zoom_factor = getattr(config, 'PDF_ZOOM_FACTOR', 1.5)
        self.llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini")
        self.mcq_batch_size = getattr(config, 'MCQ_BATCH_SIZE', 10)
        self.streaming_mode = getattr(config, 'MCQ_STREAMING_MODE', True)
        logger.info(f"MCQTranslatorFileService initialized with max_concurrent_translations={self.max_concurrent_translations}, "
                   f"batch_size={self.mcq_batch_size}, streaming_mode={self.streaming_mode}")

    async def translate_text_from_file(self, pdf_file_content: bytes, filename: str, username: str = None,
                                      total_questions: int = None, source_language: str = None, destination_language: str = None,
                                      request_id: str = None, translation_id: str = None) -> Dict:
        """
        Translate text content from an uploaded PDF file from one language to another.

        Args:
            pdf_file_content: PDF file content as bytes
            filename: Original filename of the PDF
            username: Username of the user performing the translation
            total_questions: Total number of questions for MCQ parsing batches
            source_language: Source language of the content
            destination_language: Target language to translate to
            request_id: Request ID from the API for tracking
            translation_id: Translation ID from the API for file organization

        Returns:
            Dict: Result of the translation operation including translation_id
        """
        # Use the provided translation_id from the API (no duplicate generation)
        if not translation_id:
            translation_id = str(uuid.uuid4())  # Fallback if not provided

        # Use the provided request_id from the API (no duplicate generation)
        if not request_id:
            request_id = str(uuid.uuid4())  # Fallback if not provided
        
        logger.info(f"[REQUEST:{request_id}] Starting file-based translation with ID {translation_id}, "
                   f"source_language={source_language}, destination_language={destination_language}")

        try:
            # Create temporary file for the uploaded PDF
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_pdf:
                temp_pdf.write(pdf_file_content)
                temp_pdf_path = temp_pdf.name

            logger.info(f"[REQUEST:{request_id}] Saved uploaded PDF to temporary file: {temp_pdf_path}")

            # Create output directories using the translation_id as the identifier
            output_dir = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, "translations", translation_id)
            os.makedirs(output_dir, exist_ok=True)

            # Convert PDF to column images
            pdf_converter = PDFImageConverter()
            conversion_result = pdf_converter.convert_and_upload(
                pdf_path=temp_pdf_path,
                book_id="translations",
                chapter_id=translation_id,
                res_id="main",
                zoom=self.pdf_zoom_factor
            )

            if conversion_result["status"] != "success":
                logger.error(f"[REQUEST:{request_id}] PDF conversion failed: {conversion_result['message']}")
                return {"status": "error", "message": conversion_result["message"]}

            # Get the column image URLs
            col_img_urls = conversion_result.get("cropped_image_urls", [])
            if not col_img_urls:
                logger.error(f"[REQUEST:{request_id}] No column images found")
                return {"status": "error", "message": "No column images found"}


            logger.info(f"[REQUEST:{request_id}] Found {len(col_img_urls)} column images")

            # Extract text from images in parallel
            text_extractor = MCQTextExtractorService()
            text_files = await text_extractor._extract_text_parallel(col_img_urls, request_id, "main", translation_id, "translations")

            if not text_files:
                logger.error(f"[REQUEST:{request_id}] No text files were created")
                return {"status": "error", "message": "No text files were created"}



            # Merge all text files into one combined file (now returns S3 path directly)
            s3_combined_path = await text_extractor._merge_text_files(text_files, translation_id, "main", request_id)

            if not s3_combined_path:
                logger.error(f"[REQUEST:{request_id}] Failed to create combined text file")
                return {"status": "error", "message": "Failed to create combined text file"}

            # The combined file is already in S3, so we use that path
            s3_upload_result = s3_combined_path
            logger.info(f"[REQUEST:{request_id}] Combined file already stored in S3: {s3_upload_result}")



            # Process translation
            translated_s3_path = None
            if total_questions:
                logger.info(f"[REQUEST:{request_id}] Starting MCQ translation for {total_questions} questions")

                if True:  # Simplified condition to replace memory check
                    # Process MCQ translation with streaming approach
                    from utils.s3_utils import get_s3_path
                    full_s3_path = get_s3_path(s3_upload_result)
                    logger.info(
                        f"[REQUEST:{request_id}] Processing MCQ translation with streaming approach from: {full_s3_path}")

                    try:
                        # Use streaming approach to avoid loading entire content into memory
                        translated_s3_path = await self._process_translation(
                            full_s3_path, total_questions, "main", translation_id,
                            "translations", request_id, s3_combined_path, username, source_language, destination_language
                        )

                        if translated_s3_path:
                            logger.info(
                                f"[REQUEST:{request_id}] MCQ translation completed successfully, file uploaded to: {translated_s3_path}")
                        else:
                            logger.warning(f"[REQUEST:{request_id}] MCQ translation failed or file upload failed")

                    except Exception as e:
                        logger.error(f"[REQUEST:{request_id}] Error during streaming MCQ translation: {e}")
                        logger.error(traceback.format_exc())
                        logger.warning(f"[REQUEST:{request_id}] Skipping MCQ translation due to processing error")
                        translated_s3_path = None


            else:
                logger.info(f"[REQUEST:{request_id}] Skipping MCQ translation as total_questions not provided")

            # Clean up local files (s3_combined_path is an S3 path, not a local file, so don't include it)
            text_extractor._cleanup_local_files(text_files)

            # Clean up temporary PDF file
            try:
                os.unlink(temp_pdf_path)
            except:
                pass

            # Clean up the entire translation folder after successful S3 upload
            try:
                import shutil
                translation_folder = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, "translations", translation_id)
                if os.path.exists(translation_folder):
                    shutil.rmtree(translation_folder)
                    logger.info(f"[REQUEST:{request_id}] Successfully deleted local translation folder: {translation_folder}")
                else:
                    logger.warning(f"[REQUEST:{request_id}] Translation folder not found for cleanup: {translation_folder}")
            except Exception as cleanup_error:
                logger.error(f"[REQUEST:{request_id}] Error cleaning up translation folder: {cleanup_error}")
                # Don't fail the entire process if cleanup fails
                pass

            logger.info(f"[REQUEST:{request_id}] MCQ file translation completed successfully")

            # Prepare return response
            response = {
                "status": "success",
                "message": f"Successfully translated text from {len(col_img_urls)} images",
                "translation_id": translation_id,
                "original_s3_path": s3_upload_result,
                "translated_s3_path": translated_s3_path,
                "total_images": len(col_img_urls),
                "text_files_created": len(text_files),
                "filename": filename
            }

            # Add translation processing information if it was performed
            if total_questions:
                response["translation_processing"] = {
                    "total_questions": total_questions,
                    "source_language": source_language,
                    "destination_language": destination_language,
                    "translated_s3_path": translated_s3_path,
                    "translation_completed": translated_s3_path is not None
                }

            return response
        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error during file translation: {str(e)}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}
        finally:
            logger.info(f"[REQUEST:{request_id}] File translation process completed")

    async def _process_translation(self, s3_text_path: str, total_questions: int, resource_id: str,
                                           chapter_id: str, book_id: str, request_id: str, s3_combined_path: str,
                                           username: str, source_language: str, destination_language: str) -> Optional[str]:
        """
        Process translation with streaming approach to minimize memory usage.

        Args:
            s3_text_path: S3 path to the combined text file
            total_questions: Total number of questions
            resource_id: Resource ID
            chapter_id: Chapter ID
            book_id: Book ID
            request_id: Request ID for logging
            s3_combined_path: S3 path to the combined text file
            username: Username of the user performing the translation
            source_language: Source language of the content
            destination_language: Target language to translate to

        Returns:
            Optional[str]: S3 path to the translated text file, or None if failed
        """
        try:
            # Create output directory for batch files
            output_dir = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id), str(resource_id), "translation")
            os.makedirs(output_dir, exist_ok=True)



            # Calculate batch sizes based on total questions
            batch_size = self.mcq_batch_size
            num_batches = (total_questions + batch_size - 1) // batch_size  # Ceiling division

            logger.info(f"[REQUEST:{request_id}] Processing translation in {num_batches} batches of {batch_size} questions each")

            text_content = read_file_from_s3(s3_text_path)
            text_str = text_content.decode('utf-8')
            # Process each batch
            batch_text_files = []
            for batch_num in range(1, num_batches + 1):
                start_question = (batch_num - 1) * batch_size + 1
                end_question = min(batch_num * batch_size, total_questions)

                logger.info(f"[REQUEST:{request_id}] Processing batch {batch_num}/{num_batches} (questions {start_question}-{end_question})")

                # Call LLM for this batch using streaming approach
                batch_text_content = await self._call_llm_for_translation(
                    text_str, start_question, end_question, request_id, source_language, destination_language
                )

                if batch_text_content:
                    # Save batch text to file
                    batch_text_file = os.path.join(output_dir, f"translated_batch_{batch_num}.txt")
                    with open(batch_text_file, "w", encoding="utf-8") as f:
                        f.write(batch_text_content)

                    batch_text_files.append(batch_text_file)
                    logger.info(f"[REQUEST:{request_id}] Saved batch {batch_num} translation to {batch_text_file}")
                else:
                    logger.error(f"[REQUEST:{request_id}] Failed to translate batch {batch_num}")



            # Combine all batch text files
            combined_text_path = os.path.join(output_dir, f"translated_combined_{source_language}_to_{destination_language}.txt")
            with open(combined_text_path, "w", encoding="utf-8") as outfile:
                for batch_file in batch_text_files:
                    with open(batch_file, "r", encoding="utf-8") as infile:
                        outfile.write(infile.read())
                        outfile.write("\n\n")

            logger.info(f"[REQUEST:{request_id}] Combined all batch translations to {combined_text_path}")

            # Upload the combined text file to S3
            s3_text_path = upload_file_to_s3(
                local_file_path=combined_text_path,
                book_id=book_id,
                chapter_id=chapter_id,
                res_id=resource_id,
                file_name=f"translated_{source_language}_to_{destination_language}.txt",
                is_quiz_image=False
            )

            if s3_text_path:
                logger.info(f"[REQUEST:{request_id}] Successfully uploaded combined translation to S3: {s3_text_path}")

                # Clean up local batch files after successful S3 upload
                try:
                    for batch_file in batch_text_files:
                        if os.path.exists(batch_file):
                            os.unlink(batch_file)
                    if os.path.exists(combined_text_path):
                        os.unlink(combined_text_path)
                    logger.info(f"[REQUEST:{request_id}] Cleaned up local translation batch files")
                except Exception as cleanup_error:
                    logger.error(f"[REQUEST:{request_id}] Error cleaning up batch files: {cleanup_error}")

                return s3_text_path
            else:
                logger.error(f"[REQUEST:{request_id}] Failed to upload combined translation to S3")
                return None

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error in translation streaming: {str(e)}")
            logger.error(traceback.format_exc())
            return None

    async def _call_llm_for_translation(self, text_content: str, start_question: int, end_question: int,
                                                request_id: str, source_language: str, destination_language: str) -> Optional[str]:
        """
        Call LLM to translate a batch of questions with streaming to minimize memory usage.

        Args:
            s3_text_path: S3 path to the text file
            start_question: Start question number
            end_question: End question number
            request_id: Request ID for logging
            source_language: Source language of the content
            destination_language: Target language to translate to

        Returns:
            Optional[str]: Translated text content, or None if failed
        """
        try:
            if not text_content:
                logger.error(f"[REQUEST:{request_id}] Failed to read text content from S3: {s3_text_path}")
                return None
            # Create a prompt for the LLM
            prompt = translator_prompt(
                text_content,
                start_question,
                end_question,
                source_language,
                destination_language
            )

            logger.info(f"[REQUEST:{request_id}] Calling LLM for translation batch {start_question}-{end_question}")

            # Use a more memory-efficient approach with streaming
            llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini")
            response = llm.invoke([HumanMessage(content=prompt)])

            if response and hasattr(response, 'content'):
                return response.content
            else:
                logger.error(f"[REQUEST:{request_id}] Empty or invalid response from LLM")
                return None

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error calling LLM for translation: {str(e)}")
            logger.error(traceback.format_exc())
            return None

    async def get_translated_text(self, translation_id: str) -> Dict:
        """
        Read the translated text for a translation ID.

        Args:
            translation_id: Translation ID (UUID)

        Returns:
            Dict: Text content or error message
        """
        try:
            # The file should be at: supload/pdfextracts/translations/{translation_id}/main/extractedImages/translated_{source}_to_{destination}.txt
            # Since we don't know the exact language pair, we'll try common language combinations

            from utils.s3_utils import get_s3_path

            # Common language combinations to try
            language_combinations = [
                "English_to_Hindi", "Hindi_to_English",
                "English_to_Tamil", "Tamil_to_English",
                "English_to_Telugu", "Telugu_to_English",
                "English_to_Kannada", "Kannada_to_English",
                "English_to_Malayalam", "Malayalam_to_English",
                "English_to_Marathi", "Marathi_to_English",
                "English_to_Bengali", "Bengali_to_English",
                "English_to_Gujarati", "Gujarati_to_English",
                "English_to_Urdu", "Urdu_to_English",
                "English_to_Assamese", "Assamese_to_English"
            ]

            # Try to find the translated file by checking each possible filename
            base_s3_path = f"supload/pdfextracts/translations/{translation_id}/main/extractedImages"

            translated_file_path = None
            relative_s3_path = None

            for lang_combo in language_combinations:
                filename = f"translated_{lang_combo}.txt"
                relative_path = f"{base_s3_path}/{filename}"
                full_s3_path = get_s3_path(relative_path)

                # Check if file exists using sudo test
                success, _ = run_sudo_command(
                    ["sudo", "test", "-f", full_s3_path],
                    f"Checking if file exists: {full_s3_path}"
                )

                if success:
                    translated_file_path = full_s3_path
                    relative_s3_path = relative_path
                    logger.info(f"Found translated file: {translated_file_path}")
                    break

            if not translated_file_path:
                # Try to list files in the directory to see what's actually there
                base_dir_full = get_s3_path(base_s3_path)
                list_success, list_output = run_sudo_command(
                    ["sudo", "ls", "-la", base_dir_full],
                    f"Listing directory contents: {base_dir_full}"
                )

                if list_success:
                    logger.info(f"Directory contents for {base_dir_full}:\n{list_output}")

                    # Look for any file that starts with "translated_" in the output
                    lines = list_output.split('\n')
                    for line in lines:
                        if 'translated_' in line and '.txt' in line:
                            # Extract filename from ls output
                            parts = line.split()
                            if len(parts) >= 9:  # ls -la output has at least 9 parts
                                filename = parts[-1]  # Last part is the filename
                                if filename.startswith('translated_') and filename.endswith('.txt'):
                                    relative_path = f"{base_s3_path}/{filename}"
                                    translated_file_path = get_s3_path(relative_path)
                                    relative_s3_path = relative_path
                                    logger.info(f"Found translated file from directory listing: {translated_file_path}")
                                    break

                if not translated_file_path:
                    logger.error(f"No translated files found for translation ID {translation_id}")
                    logger.error(f"Searched in directory: {base_dir_full}")
                    return {"status": "error", "message": f"No translated files found for translation ID {translation_id}"}

            # Read the file content
            content = read_file_from_s3(translated_file_path)
            if content is None:
                logger.error(f"Failed to read translated file: {translated_file_path}")
                return {"status": "error", "message": "Failed to read translated file"}

            # Decode the content
            text_content = content.decode('utf-8')

            return {
                "status": "success",
                "content": text_content,
                "translation_id": translation_id,
                "file_path": relative_s3_path
            }

        except Exception as e:
            logger.error(f"Error getting translated text for translation ID {translation_id}: {e}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}
