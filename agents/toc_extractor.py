"""
TOC Extractor

This module handles the extraction of table of contents from PDF files.
It converts PDFs to images, extracts TOC from each image, and saves the result as JSON.
"""

import logging
import os
import json
import asyncio
import uuid
import re
import shutil
from typing import Dict, List, Optional, Any

import config
from agents.utils.pdf_helpers import PDFImageConverter
from agents.core.extractor import ExtractorAgent
from agents.schemas.agent_prompts import toc_extractor_prompt
from utils.s3_utils import write_text_to_s3

# Get logger instance
logger = logging.getLogger(__name__)


class TOCExtractor:
    """Class to handle TOC extraction from PDF files"""

    def __init__(self):
        """Initialize the TOC extractor"""
        self.extractor = ExtractorAgent()
        self.TOC_EXTRACTOR_PROMPT = toc_extractor_prompt()

    def _fix_page_number_format(
            self,
            toc_entries: List[Dict[str, Any]],
            starting_page_number: Optional[int] = None,
            toc_page_numbers: Optional[str] = None,
            pdf_total_pages: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """
        Works on Python 3.9+ (no PEP-604 | unions).

        • Detects logical page restarts (current start ≤ previous start).
        • Tracks `carry_diff` – the page-block width last seen on a restart.
        • If the previous entry has no end_page, uses `carry_diff`
          (else 1) to advance and fills the gap.
        """

        numeric: List[Dict[str, Any]] = [
            e for e in toc_entries
            if re.fullmatch(r"\d+", e.get("page", ""))  # “N”
               or re.fullmatch(r"\d+\s*-\s*\d*", e.get("page", ""))  # “N-M”
        ]
        if not numeric:
            return []

        first_logical: int = int(numeric[0]["page"].split("-")[0].strip())
        diff_offset: int = (starting_page_number or first_logical) - first_logical

        fixed: List[Dict[str, Any]] = []
        prev: Optional[Dict[str, Any]] = None  # pointer to previous entry
        carry_diff: Optional[int] = None  # width remembered on restart

        for idx, entry in enumerate(numeric):
            parts = entry["page"].split("-")
            start_page: int = int(parts[0].strip())
            end_page: Optional[int] = int(parts[1].strip()) if len(parts) > 1 and parts[1].strip() else None

            if idx == 0:
                actual_start = starting_page_number or start_page
            else:
                restart = start_page <= prev["start_page"]  # type: ignore[index]

                if restart:
                    if "end_page" in prev:  # previous width known
                        inc = prev["end_page"]  # type: ignore[index]
                        carry_diff = inc  # update memory
                    else:  # previous was single
                        inc = carry_diff if carry_diff else 1

                    actual_start = prev["actual_start_page"] + inc  # type: ignore[index]

                    if "end_page" not in prev:
                        prev_len = inc
                        prev["end_page"] = prev["start_page"] + prev_len - 1
                        prev["actual_end_page"] = actual_start - 1
                else:
                    # straight gap advance
                    logical_gap = start_page - prev["start_page"]  # type: ignore[index]
                    actual_start = prev["actual_start_page"] + logical_gap  # type: ignore[index]

            if end_page is not None:
                actual_end = actual_start + (end_page - start_page)
                entry["end_page"] = end_page
                entry["actual_end_page"] = actual_end

            entry["start_page"] = start_page
            entry["actual_start_page"] = actual_start
            entry["original_page"] = entry["page"]
            entry["first_page_number"] = first_logical
            entry["starting_page_number"] = starting_page_number
            entry["page_difference"] = diff_offset

            fixed.append(entry)
            prev = entry  # move pointer

        # Apply TOC page count adjustment after all calculations are complete
        if toc_page_numbers and len(fixed) >= 2:
            try:
                page_numbers = [int(p.strip()) for p in toc_page_numbers.split(',') if p.strip()]
                if len(page_numbers) == 2:
                    # Check if first and second entries have the same start_page
                    first_start_page = fixed[0]["start_page"]
                    second_start_page = fixed[1]["start_page"]

                    if first_start_page == second_start_page:
                        # Calculate adjustment: count - 1
                        toc_page_count_adjustment = len(page_numbers) - 1  # This will be 1
                        logger.info(f"First and second entries have same start_page ({first_start_page}). Applying TOC page count adjustment: -{toc_page_count_adjustment} (from {len(page_numbers)} TOC pages: {toc_page_numbers})")

                        # Apply adjustment to all entries except the first one
                        for idx, entry in enumerate(fixed):
                            if idx > 0:  # Skip the first entry
                                entry["actual_start_page"] -= toc_page_count_adjustment
                                # Also adjust actual_end_page if it exists
                                if "actual_end_page" in entry:
                                    entry["actual_end_page"] -= toc_page_count_adjustment
                                logger.debug(f"Adjusted entry {idx}: '{entry['title']}' actual_start_page to {entry['actual_start_page']}")
                    else:
                        logger.info(f"First entry start_page ({first_start_page}) != second entry start_page ({second_start_page}). Skipping TOC page count adjustment.")
            except (ValueError, TypeError) as e:
                logger.warning(f"Failed to parse toc_page_numbers for adjustment: {toc_page_numbers}. Error: {e}")

        # Calculate actual_end_page for entries that don't have end_page (only when end_page was not originally given)
        for idx, entry in enumerate(fixed):
            if "actual_end_page" not in entry:
                if idx < len(fixed) - 1:  # Not the last entry
                    # Set actual_end_page to next entry's actual_start_page - 1
                    next_entry = fixed[idx + 1]
                    entry["actual_end_page"] = next_entry["actual_start_page"] - 1
                    logger.debug(f"Calculated actual_end_page for '{entry['title']}': {entry['actual_end_page']} (next start - 1)")
                else:
                    # For the last entry, use PDF total pages if available and end_page is not given
                    if pdf_total_pages and "end_page" not in entry:
                        entry["actual_end_page"] = pdf_total_pages
                        logger.debug(f"Set actual_end_page for last entry '{entry['title']}': {entry['actual_end_page']} (PDF total pages)")
                    else:
                        # Fallback: Set it to the same as actual_start_page (single page)
                        entry["actual_end_page"] = entry["actual_start_page"]
                        logger.debug(f"Set actual_end_page for last entry '{entry['title']}': {entry['actual_end_page']} (fallback: same as start)")

        # Additional check: if calculated actual_end_page exceeds PDF total pages, cap it to PDF total pages
        if pdf_total_pages:
            for entry in fixed:
                if "actual_end_page" in entry and entry["actual_end_page"] > pdf_total_pages:
                    original_end_page = entry["actual_end_page"]
                    entry["actual_end_page"] = pdf_total_pages
                    logger.debug(f"Capped actual_end_page for '{entry['title']}' from {original_end_page} to {pdf_total_pages} (PDF total pages limit)")

        return fixed

    def _cleanup_extraction_folder(self, extraction_id: str) -> None:
        """
        Clean up the extraction folder - No longer needed since we use S3 storage

        Args:
            extraction_id: Extraction ID
        """
        # No cleanup needed since we're using S3 storage instead of local directories
        logger.debug(f"Cleanup not needed for extraction {extraction_id} - using S3 storage")

    async def process_pdf_and_extract_toc(self, pdf_file_path: str, extraction_id: str,
                                    toc_page_numbers: Optional[str] = None,
                                    starting_page_number: Optional[int] = None) -> Dict:
        """
        Process a PDF file and extract the table of contents.

        Args:
            pdf_file_path: Path to the PDF file
            extraction_id: ID for this extraction
            toc_page_numbers: Optional comma-separated list of page numbers containing TOC
            starting_page_number: Optional starting page number for the PDF

        Returns:
            Dict: Result of the extraction process
        """
        try:
            # Generate a unique ID for this extraction
            logger.info(f"Starting TOC extraction for PDF: {pdf_file_path} with ID: {extraction_id}")

            # Initialize PDF converter
            pdf_converter = PDFImageConverter()

            # Step 1: Convert PDF to images using PDFImageConverter
            if toc_page_numbers:
                # Parse the comma-separated page numbers
                try:
                    page_numbers = [int(p.strip()) for p in toc_page_numbers.split(',') if p.strip()]
                    logger.info(f"Converting specific pages: {page_numbers}")

                    # Convert only specific pages
                    conversion_result = pdf_converter.convert_specific_pages(
                        pdf_path=pdf_file_path,
                        book_id="toc",
                        chapter_id="extraction",
                        res_id=extraction_id,
                        page_numbers=page_numbers
                    )
                except ValueError as e:
                    logger.error(f"Invalid page numbers format: {toc_page_numbers}. Error: {e}")
                    return {"status": "error", "message": f"Invalid page numbers format: {toc_page_numbers}"}
            else:
                # Convert all pages
                conversion_result = pdf_converter.convert_and_upload(
                    pdf_path=pdf_file_path,
                    book_id="toc",
                    chapter_id="extraction",
                    res_id=extraction_id
                )

            if conversion_result["status"] != "success":
                logger.error(f"PDF conversion failed: {conversion_result['message']}")
                return {"status": "error", "message": conversion_result["message"]}

            # Get the image URLs
            image_urls = conversion_result.get("image_urls", [])
            if not image_urls:
                # Try alternative keys that might contain the image URLs
                image_urls = conversion_result.get("cropped_image_urls", []) or conversion_result.get("cropped_image_urls", [])

            if not image_urls:
                logger.error("No images found in the PDF")
                return {"status": "error", "message": "No images found in the PDF"}

            logger.info(f"Successfully converted PDF to {len(image_urls)} images")

            # Create a semaphore to limit concurrent LLM calls
            semaphore = asyncio.Semaphore(3)  # Limit to 3 concurrent calls

            # Create tasks for processing each image
            tasks = []
            for image_path in image_urls:
                tasks.append(self._process_image(image_path, semaphore))

            # Run all tasks concurrently and gather results
            results = await asyncio.gather(*tasks)
            print(results)

            # Combine all TOC entries from all images
            all_toc_entries = []
            for result in results:
                if result:
                    all_toc_entries.extend(result)

            # Log the combined entries
            logger.info(f"Combined {len(all_toc_entries)} TOC entries from all images")

            # Get PDF total pages for calculating last entry's end page (only when end_page is not given)
            pdf_total_pages = None
            try:
                import fitz
                pdf_doc = fitz.open(pdf_file_path)
                pdf_total_pages = pdf_doc.page_count
                print(pdf_total_pages)
                pdf_doc.close()
                logger.info(f"PDF has {pdf_total_pages} total pages")
            except Exception as e:
                logger.warning(f"Failed to get PDF total pages: {e}")

            # Fix page number format and calculate actual page numbers
            fixed_toc_entries = self._fix_page_number_format(all_toc_entries, starting_page_number, toc_page_numbers, pdf_total_pages)

            # Log the fixed entries for debugging
            logger.info(f"Fixed TOC entries with page calculations: {fixed_toc_entries}")

            # Step 3: Save the TOC as JSON to S3
            json_content = json.dumps(fixed_toc_entries, ensure_ascii=False, indent=2)

            # Upload JSON to S3
            s3_json_path = write_text_to_s3(
                content=json_content,
                book_id="toc",
                chapter_id="extraction",
                res_id=extraction_id,
                file_name=f"{extraction_id}_toc.json",
                subfolder="extractedImages"  # Store in extractedImages folder
            )

            if s3_json_path:
                logger.info(f"TOC extraction completed successfully. Saved to S3: {s3_json_path}")
            else:
                logger.error(f"Failed to save TOC JSON to S3")
                return {"status": "error", "message": "Failed to save TOC JSON to S3"}

            # Clean up the extraction folder after processing
            self._cleanup_extraction_folder(extraction_id)

            # Ensure we're returning a list of TOC entries
            if not isinstance(fixed_toc_entries, list):
                logger.warning(f"Fixed TOC entries is not a list: {type(fixed_toc_entries)}")
                fixed_toc_entries = []

            print(f"Returning {len(fixed_toc_entries)} TOC entries")
            return {
                "status": "success",
                "message": "TOC extraction completed successfully",
                "extraction_id": extraction_id,
                "s3_path": s3_json_path,
                "results": fixed_toc_entries
            }

        except Exception as e:
            logger.error(f"Error during TOC extraction: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    async def _process_image(self, image_path: str, semaphore: asyncio.Semaphore) -> List[Dict]:
        """
        Process a single image to extract TOC entries.

        Args:
            image_path: Path to the image file
            semaphore: Semaphore to limit concurrent LLM calls

        Returns:
            List[Dict]: Extracted TOC entries from the image
        """
        try:
            async with semaphore:
                logger.info(f"Processing image: {image_path}")

                # Use the extractor to process the image with the TOC extractor prompt
                result, _ = await self.extractor._extract_with_prompt(
                    [image_path],
                    self.TOC_EXTRACTOR_PROMPT,
                    md_parse=False,
                    md_result=False
                )

                print(result)
                # Log the raw result for debugging
                logger.info(f"Raw LLM result from {image_path}: {result[:200]}...")

                # Check if the result is "NOT_TOC" which means this page is not a TOC page
                if result.strip() == "NOT_TOC":
                    logger.info(f"Image {image_path} is not a TOC page")
                    return []

                # Try to parse the result as JSON
                try:
                    # Clean up the result - sometimes the LLM adds extra text before or after the JSON
                    json_start = result.find('[')
                    json_end = result.rfind(']') + 1

                    if json_start >= 0 and json_end > json_start:
                        json_str = result[json_start:json_end]
                        logger.info(f"Extracted JSON string: {json_str[:200]}...")
                        toc_entries = json.loads(json_str)
                    else:
                        # Try to parse the whole result
                        toc_entries = json.loads(result)

                    if isinstance(toc_entries, list):
                        logger.info(f"Successfully extracted {len(toc_entries)} TOC entries from {image_path}")
                        # Validate each entry has title and page
                        valid_entries = []
                        for entry in toc_entries:
                            if isinstance(entry, dict) and 'title' in entry and 'page' in entry:
                                valid_entries.append(entry)
                            else:
                                logger.warning(f"Invalid TOC entry format: {entry}")

                        logger.info(f"Validated {len(valid_entries)} out of {len(toc_entries)} entries")
                        return valid_entries
                    else:
                        logger.warning(f"Unexpected TOC format (not a list) from {image_path}: {toc_entries}")
                        return []
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse TOC result as JSON: {e}")
                    logger.error(f"Result content: {result}")
                    return []

        except Exception as e:
            logger.error(f"Error processing image {image_path}: {e}")
            return []

    async def process_toc_extraction_by_resource(self, res_id: str) -> Dict:
        """
        Process the TOC extraction for a specific resource.

        Args:
            res_id: Resource ID to extract TOC from

        Returns:
            Dict: Result of the extraction process
        """
        try:
            # Generate a unique ID for this extraction
            extraction_id = str(uuid.uuid4())
            logger.info(f"Starting TOC extraction for resource ID: {res_id} with extraction ID: {extraction_id}")

            # Get a database session for the wscontent schema
            from db_config.db import get_session, CONTENT_SCHEMA
            from sqlalchemy import text

            db_session = next(get_session(CONTENT_SCHEMA))
            try:
                # Query the resource_dtl table to get resource details
                resource_query = text("""
                    SELECT id, chapter_id, res_link, resource_name
                    FROM wscontent.resource_dtl
                    WHERE id = :res_id
                    LIMIT 1
                """)

                resource_result = db_session.execute(resource_query, {"res_id": res_id})
                resource_row = resource_result.fetchone()

                if not resource_row:
                    logger.warning(f"No resource found with ID: {res_id}")
                    return {"status": "error", "message": f"Resource with ID {res_id} not found"}

                # Extract resource details
                resource_id = resource_row[0]
                chapter_id = resource_row[1]
                file_path = resource_row[2]
                resource_name = resource_row[3]

                # Query the chapters_mst table to get the book_id
                chapter_query = text("""
                    SELECT book_id
                    FROM wscontent.chapters_mst
                    WHERE id = :chapter_id
                    LIMIT 1
                """)

                chapter_result = db_session.execute(chapter_query, {"chapter_id": chapter_id})
                chapter_row = chapter_result.fetchone()

                if not chapter_row:
                    logger.error(f"Chapter with ID {chapter_id} not found in database")
                    return {"status": "error", "message": f"Chapter with ID {chapter_id} not found"}

                # Extract book_id
                book_id = chapter_row[0]
                logger.info(f"Found book ID: {book_id}")

            finally:
                # Close the database session
                db_session.close()
                logger.debug("Database session closed after resource and chapter queries")

            # Get the full S3 path to the PDF
            from utils.s3_utils import get_s3_path, read_file_from_s3

            # Construct the S3 path to the PDF
            s3_path = file_path
            full_s3_path = get_s3_path(s3_path)
            logger.info(f"Full S3 path to PDF: {full_s3_path}")

            # Read the PDF content from S3
            pdf_content = read_file_from_s3(full_s3_path)
            if pdf_content is None:
                logger.error(f"Failed to read PDF from S3: {full_s3_path}")
                return {"status": "error", "message": f"Failed to read PDF from S3: {s3_path}"}

            # Create a temporary file to store the PDF content
            import tempfile
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_path = temp_file.name
                temp_file.write(pdf_content)

            logger.info(f"PDF content written to temporary file: {temp_path}")

            try:
                # Process the PDF and extract TOC
                result = await self.process_pdf_and_extract_toc(temp_path, extraction_id)

                # Add resource details to the result
                result["resource_id"] = resource_id
                result["chapter_id"] = chapter_id
                result["book_id"] = book_id
                result["resource_name"] = resource_name

                return result
            finally:
                # Clean up the temporary file
                try:
                    os.unlink(temp_path)
                    logger.info(f"Temporary PDF file removed: {temp_path}")
                except Exception as e:
                    logger.warning(f"Failed to remove temporary PDF file: {e}")

        except Exception as e:
            logger.error(f"Error during TOC extraction for resource ID {res_id}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    async def process_toc_extraction_by_s3_path(self, s3_path: str,
                                     toc_page_numbers: Optional[str] = None,
                                     starting_page_number: Optional[int] = None) -> Dict:
        """
        Process the TOC extraction for a PDF file at a specific S3 path.

        Args:
            s3_path: S3 path to the PDF file
            toc_page_numbers: Optional comma-separated list of page numbers containing TOC
            starting_page_number: Optional starting page number for the PDF

        Returns:
            Dict: Result of the extraction process
        """
        try:
            # Generate a unique ID for this extraction
            extraction_id = str(uuid.uuid4())
            logger.info(f"Starting TOC extraction for S3 path: {s3_path} with extraction ID: {extraction_id}")

            if toc_page_numbers:
                logger.info(f"Using specific TOC page numbers: {toc_page_numbers}")
            if starting_page_number:
                logger.info(f"Using starting page number: {starting_page_number}")

            # Get the full S3 path to the PDF
            from utils.s3_utils import get_s3_path, read_file_from_s3

            # Get the full S3 path
            full_s3_path = get_s3_path(s3_path)
            logger.info(f"Full S3 path to PDF: {full_s3_path}")

            # Read the PDF content from S3
            pdf_content = read_file_from_s3(full_s3_path)
            if pdf_content is None:
                logger.error(f"Failed to read PDF from S3: {full_s3_path}")
                return {"status": "error", "message": f"Failed to read PDF from S3: {s3_path}"}

            # Create a temporary file to store the PDF content
            import tempfile
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_path = temp_file.name
                temp_file.write(pdf_content)

            logger.info(f"PDF content written to temporary file: {temp_path}")

            try:
                # Process the PDF and extract TOC with the new parameters
                result = await self.process_pdf_and_extract_toc(
                    temp_path,
                    extraction_id,
                    toc_page_numbers=toc_page_numbers,
                    starting_page_number=starting_page_number
                )

                # Add S3 path to the result
                result["s3_path"] = s3_path

                return result
            finally:
                # Clean up the temporary file
                try:
                    os.unlink(temp_path)
                    logger.info(f"Temporary PDF file removed: {temp_path}")
                except Exception as e:
                    logger.warning(f"Failed to remove temporary PDF file: {e}")

        except Exception as e:
            logger.error(f"Error during TOC extraction for S3 path {s3_path}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}