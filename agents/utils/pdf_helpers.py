# File: agents/utils/pdf_helpers.py
import logging
import os
from typing import List, Optional

import fitz
import tempfile
from PIL import Image

import config
import external_api_endpoints
from ..utils.image_utils import crop_image
from utils.s3_utils import read_file_from_s3, get_s3_path, upload_file_to_s3
import tempfile

# Get logger instance
logger = logging.getLogger(__name__)


class PDFImageConverter:
    """Utility class to convert PDF pages to images and upload to API"""

    def __init__(self, progress_tracker=None, task_id=None):
        self.base_url = config.BASE_URL
        # Keep the API URL for backward compatibility
        self.api_url = config.BASE_URL + external_api_endpoints.IMAGE_UPLOAD_API_ENDPOINT
        self.progress_tracker = progress_tracker
        self.task_id = task_id

    def track_progress(self, step, progress=None):
        """Track progress if a progress tracker is available."""
        # Always log the step
        logger.info(f"PDF Converter: {step} ({progress if progress else 'N/A'}%)")

        # If progress tracker is available, add the step
        if self.progress_tracker and self.task_id:
            logger.info(f"PDF Converter sending progress update to tracker for task {self.task_id}: {step}")
            try:
                # Force a small delay to ensure updates are processed separately
                import time
                time.sleep(0.1)  # 100ms delay to separate updates

                # Add the step to the progress tracker
                self.progress_tracker.add_step(self.task_id, step, progress)

                # Verify the step was added
                task = self.progress_tracker.get_task(self.task_id)
                if task and 'steps' in task and any(s.get('step') == step for s in task['steps']):
                    logger.info(f"Step '{step}' successfully added to task {self.task_id}")
                else:
                    logger.warning(f"Step '{step}' may not have been added to task {self.task_id}")
            except Exception as e:
                logger.error(f"Error sending progress update from PDF Converter: {e}")
        else:
            logger.warning(f"PDF Converter: No progress tracker available for step: {step}")

    def convert_and_upload(self, pdf_path: str, book_id: str, chapter_id: str, res_id: str, zoom: float = None) -> dict:
        """
        Convert PDF pages to images and upload directly to S3

        Args:
            pdf_path: Path to the PDF file
            book_id: Book identifier
            chapter_id: Chapter identifier
            res_id: Resource identifier
            zoom: Zoom factor for better resolution (uses config default if None)

        Returns:
            dict: Result containing status and S3 file paths
        """
        try:
            # Use config default zoom factor if not specified
            if zoom is None:
                zoom = getattr(config, 'PDF_ZOOM_FACTOR', 1)

            logger.info(f"[PDFImageConverter] Starting PDF conversion for book_id: {book_id}, chapter_id: {chapter_id}, res_id: {res_id}, zoom: {zoom}")

            # Check if images already exist in S3
            from utils.s3_utils import list_files_in_s3_directory

            # Check for existing page images in S3
            existing_page_images = list_files_in_s3_directory(
                book_id=str(book_id),
                chapter_id=str(chapter_id),
                res_id=str(res_id),
                subfolder="",  # Root folder for page images
                file_pattern="page_*.png"
            )

            # Check for existing cropped images in S3
            existing_cropped_images = list_files_in_s3_directory(
                book_id=str(book_id),
                chapter_id=str(chapter_id),
                res_id=str(res_id),
                subfolder="",  # Root folder for cropped images
                file_pattern="page_*_col_*.png"
            )

            if len(existing_page_images) > 0 and len(existing_cropped_images) > 0:
                # Images already exist in S3, return them
                logger.info(f"[PDFImageConverter] Found {len(existing_page_images)} existing page images and {len(existing_cropped_images)} cropped images in S3")
                self.track_progress(f"Found existing images in S3", 38)

                # Sort the paths to ensure they're in the correct order
                existing_page_images.sort()
                existing_cropped_images.sort()

                # Get page count from the number of regular images
                page_count = len(existing_page_images)

                self.track_progress(f"Using {page_count} existing pages from S3", 39)

                return {
                    "status": "success",
                    "message": f"Using {page_count} existing pages from S3",
                    "image_urls": existing_page_images,
                    "cropped_image_urls": existing_cropped_images,
                    "total_pages": page_count
                }

            logger.info(f"[PDFImageConverter] PDF path: {pdf_path}, zoom factor: {zoom}")

            # Check if the file exists locally first
            if os.path.isfile(pdf_path):
                logger.info(f"[PDFImageConverter] PDF file exists locally: {pdf_path}")
                self.track_progress(f"Using local PDF file: {pdf_path}", 32)
                temp_path = pdf_path
            else:
                # Try to get the file from S3
                logger.info(f"[PDFImageConverter] PDF file not found locally, trying S3")

                # Get the full S3 path
                full_s3_path = get_s3_path(pdf_path)
                logger.info(f"[PDFImageConverter] Full S3 mount path: {full_s3_path}")

                # Read the PDF file using sudo
                logger.info(f"[PDFImageConverter] Reading PDF from S3 mount path using sudo")
                self.track_progress(f"Reading PDF file: {pdf_path}", 32)

                # Read the file content using sudo
                self.track_progress(f"Reading PDF file from S3: {pdf_path}", 33)
                pdf_content = read_file_from_s3(full_s3_path)
                if pdf_content is None:
                    logger.error(f"[PDFImageConverter] CRITICAL ERROR: Failed to read PDF file from S3: {full_s3_path}")
                    self.track_progress(f"Failed to read PDF file from S3: {full_s3_path}", 100)
                    return {
                        "status": "error",
                        "message": f"Failed to read PDF file from S3: {full_s3_path}"
                    }

                # Create a temporary file to work with
                self.track_progress(f"Creating temporary file for PDF processing", 34)
                with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                    temp_path = temp_file.name
                    temp_file.write(pdf_content)
                    self.track_progress(f"PDF content written to temporary file", 35)

            # Open the PDF from the temporary file
            logger.info(f"[PDFImageConverter] Opening PDF from temporary file: {temp_path}")
            self.track_progress(f"Opening PDF document", 36)
            pdf_document = fitz.open(temp_path)

            # If we get here, we need to convert the PDF
            self.track_progress(f"Starting PDF conversion", 37)

            # We already have the PDF document open from the temporary file
            logger.info(f"[PDFImageConverter] Using already opened PDF document from temporary file")

            # Store the page count before processing
            page_count = pdf_document.page_count
            logger.info(f"[PDFImageConverter] PDF has {page_count} pages to process")
            self.track_progress(f"PDF has {page_count} pages to process", 38)

            # Store S3 paths for uploaded images
            image_s3_paths = []
            cropped_image_s3_paths = []

            # Convert each page to image
            for page_number in range(page_count):
                progress = 33 + int((page_number / page_count) * 5)  # Progress from 33% to 38%
                self.track_progress(f"Converting page {page_number + 1} of {page_count}", progress)

                page = pdf_document[page_number]
                mat = fitz.Matrix(zoom, zoom)
                pix = page.get_pixmap(matrix=mat)
                img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

                # Save page image to temporary file and upload to S3
                self.track_progress(f"Uploading page {page_number + 1} image to S3", progress)
                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                    img.save(temp_file.name, format="PNG")

                    # Upload to S3
                    s3_path = upload_file_to_s3(
                        temp_file.name,
                        str(book_id),
                        str(chapter_id),
                        str(res_id),
                        file_name=f"page_{page_number + 1}.png",
                        is_quiz_image=False  # These are source page images, not quiz images
                    )

                    if s3_path:
                        image_s3_paths.append(s3_path)

                    # Clean up temporary file
                    try:
                        os.unlink(temp_file.name)
                    except:
                        pass

                # Crop the image using the updated margins
                self.track_progress(f"Cropping page {page_number + 1}", progress)
                column_images = crop_image(img)

                # Process each column image separately
                for col_idx, col_img in enumerate(column_images):
                    # Save cropped image to temporary file and upload to S3
                    self.track_progress(f"Uploading cropped column {col_idx + 1} of page {page_number + 1} to S3", progress)
                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_cropped_file:
                        col_img.save(temp_cropped_file.name, format="PNG")

                        # Upload to S3
                        s3_cropped_path = upload_file_to_s3(
                            temp_cropped_file.name,
                            str(book_id),
                            str(chapter_id),
                            str(res_id),
                            file_name=f"page_{page_number + 1}_col_{col_idx + 1}.png",
                            is_quiz_image=False  # These are source column images, not quiz images
                        )

                        if s3_cropped_path:
                            cropped_image_s3_paths.append(s3_cropped_path)

                        # Clean up temporary file
                        try:
                            os.unlink(temp_cropped_file.name)
                        except:
                            pass

            # Close the PDF
            pdf_document.close()
            logger.info(f"[PDFImageConverter] PDF document closed")

            # Clean up the temporary file if it's not the original PDF
            if temp_path != pdf_path:
                try:
                    os.unlink(temp_path)
                    logger.info(f"[PDFImageConverter] Temporary file removed: {temp_path}")
                except Exception as e:
                    logger.warning(f"[PDFImageConverter] Failed to remove temporary file: {e}")
            else:
                logger.info(f"[PDFImageConverter] Not removing original PDF file: {temp_path}")

            logger.info(f"[PDFImageConverter] Conversion complete: {page_count} pages converted and uploaded to S3")
            logger.info(f"[PDFImageConverter] Generated {len(image_s3_paths)} main images and {len(cropped_image_s3_paths)} cropped images")

            # Log a sample of the S3 paths for debugging
            if image_s3_paths:
                sample_paths = image_s3_paths[:min(2, len(image_s3_paths))]
                logger.info(f"[PDFImageConverter] Sample S3 image paths: {sample_paths}")

            if cropped_image_s3_paths:
                sample_cropped = cropped_image_s3_paths[:min(2, len(cropped_image_s3_paths))]
                logger.info(f"[PDFImageConverter] Sample S3 cropped image paths: {sample_cropped}")

            self.track_progress(f"Completed conversion of {page_count} pages", 39)

            return {
                "status": "success",
                "message": f"Converted and uploaded {page_count} pages to S3",
                "image_urls": image_s3_paths,
                "cropped_image_urls": cropped_image_s3_paths,
                "total_pages": page_count
            }

        except Exception as e:
            logger.error(f"[PDFImageConverter] Error during PDF conversion: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    def convert_specific_pages(self, pdf_path: str, book_id: str, chapter_id: str, res_id: str,
                              page_numbers: List[int], zoom: float = 2.0) -> dict:
        """
        Convert specific PDF pages to images and upload to S3

        Args:
            pdf_path: Path to the PDF file
            book_id: Book identifier
            chapter_id: Chapter identifier
            res_id: Resource identifier
            page_numbers: List of page numbers to convert (1-based)
            zoom: Zoom factor for better resolution (default: 2.0)

        Returns:
            dict: Result containing status and S3 file paths
        """
        try:
            logger.info(f"[PDFImageConverter] Starting specific pages conversion for book_id: {book_id}, chapter_id: {chapter_id}, res_id: {res_id}")
            logger.info(f"[PDFImageConverter] Converting pages: {page_numbers}")

            # No need to create local directories since we're uploading to S3

            # Check if the file exists locally first
            if os.path.isfile(pdf_path):
                logger.info(f"[PDFImageConverter] PDF file exists locally: {pdf_path}")
                self.track_progress(f"Using local PDF file: {pdf_path}", 32)
                temp_path = pdf_path
            else:
                # Try to get the file from S3
                logger.info(f"[PDFImageConverter] PDF file not found locally, trying S3")

                # Get the full S3 path
                full_s3_path = get_s3_path(pdf_path)
                logger.info(f"[PDFImageConverter] Full S3 mount path: {full_s3_path}")

                # Read the PDF file
                logger.info(f"[PDFImageConverter] Reading PDF from S3 mount path")
                self.track_progress(f"Reading PDF file: {pdf_path}", 32)

                # Read the file content
                self.track_progress(f"Reading PDF file from S3: {pdf_path}", 33)
                pdf_content = read_file_from_s3(full_s3_path)
                if pdf_content is None:
                    logger.error(f"[PDFImageConverter] CRITICAL ERROR: Failed to read PDF file from S3: {full_s3_path}")
                    self.track_progress(f"Failed to read PDF file from S3: {full_s3_path}", 100)
                    return {
                        "status": "error",
                        "message": f"Failed to read PDF file from S3: {full_s3_path}"
                    }

                # Create a temporary file to work with
                self.track_progress(f"Creating temporary file for PDF processing", 34)
                with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                    temp_path = temp_file.name
                    temp_file.write(pdf_content)
                    self.track_progress(f"PDF content written to temporary file", 35)

            # Open the PDF from the temporary file
            logger.info(f"[PDFImageConverter] Opening PDF from temporary file: {temp_path}")
            self.track_progress(f"Opening PDF document", 36)
            pdf_document = fitz.open(temp_path)

            # Store the page count
            total_page_count = pdf_document.page_count
            logger.info(f"[PDFImageConverter] PDF has {total_page_count} total pages")

            # Filter valid page numbers
            valid_page_numbers = [p for p in page_numbers if 1 <= p <= total_page_count]
            if not valid_page_numbers:
                logger.error(f"[PDFImageConverter] No valid page numbers provided. Valid range is 1-{total_page_count}")
                return {
                    "status": "error",
                    "message": f"No valid page numbers provided. Valid range is 1-{total_page_count}"
                }

            logger.info(f"[PDFImageConverter] Converting {len(valid_page_numbers)} pages: {valid_page_numbers}")
            self.track_progress(f"Converting {len(valid_page_numbers)} specific pages", 38)

            # Store S3 paths
            image_s3_paths = []
            cropped_image_s3_paths = []

            # Convert each specified page to image
            for i, page_number in enumerate(valid_page_numbers):
                # PDF pages are 0-indexed
                pdf_page_idx = page_number - 1

                progress = 40 + int((i / len(valid_page_numbers)) * 50)
                self.track_progress(f"Converting page {page_number} ({i+1}/{len(valid_page_numbers)})", progress)

                page = pdf_document[pdf_page_idx]
                mat = fitz.Matrix(zoom, zoom)
                pix = page.get_pixmap(matrix=mat)
                img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

                # Save page image to temporary file and upload to S3
                self.track_progress(f"Uploading page {page_number} image to S3", progress)
                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                    img.save(temp_file.name, format="PNG")

                    # Upload to S3
                    s3_path = upload_file_to_s3(
                        temp_file.name,
                        str(book_id),
                        str(chapter_id),
                        str(res_id),
                        file_name=f"page_{page_number}.png",
                        is_quiz_image=False
                    )

                    if s3_path:
                        image_s3_paths.append(s3_path)

                    # Clean up temporary file
                    try:
                        os.unlink(temp_file.name)
                    except:
                        pass

                # Crop the image and upload cropped images to S3
                self.track_progress(f"Cropping page {page_number}", progress)
                column_images = crop_image(img)

                # Process each column image separately
                for col_idx, col_img in enumerate(column_images):
                    # Save cropped image to temporary file and upload to S3
                    self.track_progress(f"Uploading cropped column {col_idx + 1} of page {page_number} to S3", progress)
                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_cropped_file:
                        col_img.save(temp_cropped_file.name, format="PNG")

                        # Upload to S3
                        s3_cropped_path = upload_file_to_s3(
                            temp_cropped_file.name,
                            str(book_id),
                            str(chapter_id),
                            str(res_id),
                            file_name=f"page_{page_number}_col_{col_idx + 1}.png",
                            is_quiz_image=False
                        )

                        if s3_cropped_path:
                            cropped_image_s3_paths.append(s3_cropped_path)

                        # Clean up temporary file
                        try:
                            os.unlink(temp_cropped_file.name)
                        except:
                            pass

            # Close the PDF
            pdf_document.close()
            logger.info(f"[PDFImageConverter] PDF document closed")

            # Clean up the temporary file if it's not the original PDF
            if temp_path != pdf_path:
                try:
                    os.unlink(temp_path)
                    logger.info(f"[PDFImageConverter] Temporary file removed: {temp_path}")
                except Exception as e:
                    logger.warning(f"[PDFImageConverter] Failed to remove temporary file: {e}")

            logger.info(f"[PDFImageConverter] Conversion complete: {len(valid_page_numbers)} pages converted and uploaded to S3")
            logger.info(f"[PDFImageConverter] Generated {len(image_s3_paths)} main images and {len(cropped_image_s3_paths)} cropped images")

            return {
                "status": "success",
                "message": f"Converted and uploaded {len(valid_page_numbers)} specific pages to S3",
                "image_urls": image_s3_paths,
                "cropped_image_urls": cropped_image_s3_paths,
                "total_pages": len(valid_page_numbers)
            }

        except Exception as e:
            logger.error(f"[PDFImageConverter] Error during specific pages conversion: {e}")
            return {
                "status": "error",
                "message": str(e)
            }


def extract_page_number(url):
    page_name = url.split('/')[-1]
    if '.' in page_name:
        page_id = page_name.split('.')[0]  # For main URLs
    else:
        page_id = page_name  # Just in case

    # Extract the number from page_X
    try:
        return int(page_id.split('_')[1])
    except (IndexError, ValueError):
        return 0


def extract_cropped_page_number(url):
    # Extract page number from paths like "/page_7/page_7_col_2.png"
    parts = url.split('/')
    for part in parts:
        if part.startswith('page_'):
            try:
                return int(part.split('_')[1])
            except (IndexError, ValueError):
                pass
    return 0


def extract_col_number(col_url):
    col_part = col_url.split('/')[-1].split('.')[0]  # Get "page_X_col_Y"
    try:
        return int(col_part.split('_')[-1])  # Extract Y from "col_Y"
    except (IndexError, ValueError):
        return 0


class PageNumberConverter:
    """Utility class to convert between PDF page numbers and actual page numbers"""

    @staticmethod
    def get_actual_page_number(pdf_page_number: int, starting_page_number: Optional[int] = None) -> int:
        """
        Convert PDF page number (0-based) to actual page number

        Args:
            pdf_page_number: 0-based PDF page number
            starting_page_number: Optional starting page number (1 if None)

        Returns:
            int: Actual page number
        """
        if starting_page_number is None:
            starting_page_number = 1

        return pdf_page_number + starting_page_number
