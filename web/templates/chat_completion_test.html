{% extends "base.html" %}

{% block title %}Chat Completion Test | GPT Sir{% endblock %}

{% block content %}

<style>
.chat-test-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.chat-test-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.chat-test-card h1 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 2rem;
}

.test-description {
    color: #7f8c8d;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.input-section {
    margin-bottom: 2rem;
}

.input-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.prompt-textarea {
    width: 95%;
    padding: 1rem;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    resize: vertical;
    min-height: 120px;
    margin-bottom: 1rem;
}

.prompt-textarea:focus {
    outline: none;
    border-color: #3498db;
}

.send-button {
    background: #3498db;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
}

.send-button:hover:not(:disabled) {
    background: #2980b9;
}

.send-button:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}

.response-section, .error-section {
    margin-top: 2rem;
}

.response-section h3, .error-section h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.response-container, .error-container {
    background: #f8f9fa;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.response-output, .error-output {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0;
    color: #2c3e50;
}

.error-output {
    color: #e74c3c;
}

.copy-button {
    background: #27ae60;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.copy-button:hover {
    background: #229954;
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
<div class="chat-test-container">
    <div class="chat-test-card">
        <h1>Chat Completion API Test</h1>
        <p class="test-description">Test the chat completion API endpoint with your prompts</p>

        <div class="input-section">
            <label for="promptInput">Enter your prompt:</label>
            <textarea 
                id="promptInput" 
                placeholder="Type your prompt here..."
                rows="6"
                class="prompt-textarea"
            ></textarea>
            
            <button id="sendPrompt" class="send-button">
                <span class="button-text">Send Prompt</span>
                <span class="loading-spinner" style="display: none;">⏳</span>
            </button>
        </div>

        <div class="response-section" id="responseSection" style="display: none;">
            <h3>API Response:</h3>
            <div class="response-container">
                <pre id="responseOutput" class="response-output"></pre>
            </div>
            <button id="copyResponse" class="copy-button">Copy Response</button>
        </div>

        <div class="error-section" id="errorSection" style="display: none;">
            <h3>Error:</h3>
            <div class="error-container">
                <pre id="errorOutput" class="error-output"></pre>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const promptInput = document.getElementById('promptInput');
    const sendButton = document.getElementById('sendPrompt');
    const buttonText = document.querySelector('.button-text');
    const loadingSpinner = document.querySelector('.loading-spinner');
    const responseSection = document.getElementById('responseSection');
    const errorSection = document.getElementById('errorSection');
    const responseOutput = document.getElementById('responseOutput');
    const errorOutput = document.getElementById('errorOutput');
    const copyButton = document.getElementById('copyResponse');

    sendButton.addEventListener('click', async function() {
        const prompt = promptInput.value.trim();
        
        if (!prompt) {
            alert('Please enter a prompt');
            return;
        }

        // Show loading state
        sendButton.disabled = true;
        buttonText.style.display = 'none';
        loadingSpinner.style.display = 'inline';
        responseSection.style.display = 'none';
        errorSection.style.display = 'none';

        try {
            const response = await fetch('/api/chat-completion', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: prompt
                })
            });

            const data = await response.json();

            if (response.ok) {
                // Show successful response
                responseOutput.textContent = JSON.stringify(data, null, 2);
                responseSection.style.display = 'block';
                errorSection.style.display = 'none';
            } else {
                // Show error response
                errorOutput.textContent = JSON.stringify(data, null, 2);
                errorSection.style.display = 'block';
                responseSection.style.display = 'none';
            }
        } catch (error) {
            // Show network error
            errorOutput.textContent = JSON.stringify({
                error: 'Network error',
                message: error.message
            }, null, 2);
            errorSection.style.display = 'block';
            responseSection.style.display = 'none';
        } finally {
            // Reset loading state
            sendButton.disabled = false;
            buttonText.style.display = 'inline';
            loadingSpinner.style.display = 'none';
        }
    });

    // Copy response functionality
    copyButton.addEventListener('click', function() {
        const responseText = responseOutput.textContent;
        navigator.clipboard.writeText(responseText).then(function() {
            const originalText = copyButton.textContent;
            copyButton.textContent = 'Copied!';
            setTimeout(function() {
                copyButton.textContent = originalText;
            }, 2000);
        }).catch(function(err) {
            console.error('Failed to copy: ', err);
        });
    });

    // Allow Enter+Shift to send
    promptInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && e.shiftKey) {
            e.preventDefault();
            sendButton.click();
        }
    });
});
</script>
{% endblock %}
