{% extends "base.html" %}

{% block title %}Update Document Status{% endblock %}

{% block content %}
<div class="container">
  <div class="header">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
      <h1 class="page-title">Update Document Status</h1>
      <a href="/pyqs_admin/exams" class="btn btn-secondary">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
          <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
        </svg>
        Back to Exams
      </a>
    </div>
    <p class="page-description">Update the extraction status for existing documents</p>
  </div>

  <!-- Exam ID Input Form -->
  <div class="form-section">
    <form method="GET" action="/pyqs_admin/update-document-status" class="exam-form">
      <div class="form-group">
        <label for="exam_id" class="form-label">Exam ID:</label>
        <input type="number" id="exam_id" name="exam_id" value="{{ exam_id or '' }}" 
               placeholder="Enter exam ID" class="form-input" required>
        <button type="submit" class="btn btn-primary">Load Documents</button>
      </div>
    </form>
  </div>

  {% if exam %}
    <div class="exam-info">
      <h2>{{ exam.exam_name }}</h2>
      <p><strong>Subject:</strong> {{ exam.subject }} | <strong>Level:</strong> {{ exam.level }} | <strong>Grade:</strong> {{ exam.grade }}</p>
    </div>

    {% if documents %}
      <div class="documents-section">
        <h3>Documents ({{ documents|length }} found)</h3>
        
        <div class="documents-list">
          {% for document in documents %}
            <div class="document-item">
              <div class="document-info">
                <div class="document-name">
                  {% if document.month and document.shift %}
                    {{ document.year }} {{ document.month }} ({{ document.shift }})
                  {% elif document.month %}
                    {{ document.year }} {{ document.month }}
                  {% elif document.shift %}
                    {{ document.year }} ({{ document.shift }})
                  {% else %}
                    {{ document.year }}
                  {% endif %}
                </div>
                <div class="document-meta">
                  <span>Document ID: {{ document.id }}</span> | 
                  <span>Uploaded: {{ document.date_created.strftime('%d %b %Y') }}</span> |
                  <span>Current Status:
                    {% if document.extracted_content == 'EXTRACTED_ONLY_QUESTIONS' %}
                      <span class="status-badge status-questions">Questions Extracted</span>
                    {% elif document.extracted_content == 'EXTRACTED_QUES_AND_SOL' %}
                      <span class="status-badge status-both">Questions & Solutions Extracted</span>
                    {% else %}
                      <span class="status-badge status-none">Not Yet Extracted</span>
                    {% endif %}
                  </span>
                </div>
              </div>
              
              <div class="document-actions">
                <button onclick="updateDocumentStatus({{ document.id }}, 'EXTRACTED_ONLY_QUESTIONS')" 
                        class="btn btn-secondary btn-sm">
                  Set as "Extracted Questions"
                </button>
                <button onclick="updateDocumentStatus({{ document.id }}, 'EXTRACTED_QUES_AND_SOL')" 
                        class="btn btn-success btn-sm">
                  Set as "Extracted Both"
                </button>
              </div>
            </div>
          {% endfor %}
        </div>
      </div>
    {% else %}
      <div class="no-documents">
        <p>No documents found for this exam.</p>
      </div>
    {% endif %}
  {% endif %}

  {% if error %}
    <div class="error-message">
      {{ error }}
    </div>
  {% endif %}
</div>

<style>
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .header {
    margin-bottom: 30px;
    text-align: center;
  }

  .page-title {
    font-size: 2rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 8px;
  }

  .page-description {
    color: #6b7280;
    font-size: 1rem;
  }

  .form-section {
    background-color: #f9fafb;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    margin-bottom: 30px;
  }

  .exam-form {
    display: flex;
    align-items: end;
    gap: 16px;
    flex-wrap: wrap;
  }

  .form-group {
    display: flex;
    align-items: end;
    gap: 12px;
    flex-wrap: wrap;
  }

  .form-label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
    white-space: nowrap;
  }

  .form-input {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    min-width: 200px;
  }

  .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
  }

  .btn-primary {
    background-color: #10b981;
    color: white;
  }

  .btn-primary:hover {
    background-color: #059669;
  }

  .btn-secondary {
    background-color: #6b7280;
    color: white;
  }

  .btn-secondary:hover {
    background-color: #4b5563;
  }

  .btn-success {
    background-color: #10b981;
    color: white;
  }

  .btn-success:hover {
    background-color: #059669;
  }

  .btn-sm {
    padding: 6px 12px;
    font-size: 0.75rem;
  }

  .exam-info {
    background-color: #f0f9ff;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #bae6fd;
    margin-bottom: 20px;
  }

  .exam-info h2 {
    margin: 0 0 8px 0;
    color: #0c4a6e;
  }

  .exam-info p {
    margin: 0;
    color: #0369a1;
  }

  .documents-section h3 {
    margin-bottom: 16px;
    color: #111827;
  }

  .documents-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .document-item {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
  }

  .document-info {
    flex: 1;
  }

  .document-name {
    font-weight: 600;
    color: #111827;
    margin-bottom: 4px;
  }

  .document-meta {
    font-size: 0.875rem;
    color: #6b7280;
  }

  .document-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .status-badge {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .status-questions {
    background-color: #fef3c7;
    color: #92400e;
  }

  .status-both {
    background-color: #d1fae5;
    color: #065f46;
  }

  .status-none {
    background-color: #f3f4f6;
    color: #374151;
  }

  .no-documents {
    text-align: center;
    padding: 40px;
    color: #6b7280;
  }

  .error-message {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px 16px;
    border-radius: 8px;
    margin-top: 20px;
    font-size: 0.875rem;
  }

  .success-message {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #166534;
    padding: 12px 16px;
    border-radius: 8px;
    margin-top: 20px;
    font-size: 0.875rem;
  }

  @media (max-width: 768px) {
    .document-item {
      flex-direction: column;
      align-items: stretch;
    }

    .document-actions {
      justify-content: center;
    }

    .exam-form {
      flex-direction: column;
      align-items: stretch;
    }

    .form-group {
      flex-direction: column;
      align-items: stretch;
    }
  }
</style>

<script>
  function updateDocumentStatus(documentId, status) {
    if (!confirm(`Are you sure you want to update the status to "${status}"?`)) {
      return;
    }

    const statusText = status === 'EXTRACTED_ONLY_QUESTIONS' ? 'Questions Extracted' : 'Questions & Solutions Extracted';
    
    fetch('/pyqs_admin/update-document-status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        document_id: documentId,
        status: status
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert(`Status updated successfully to "${statusText}"`);
        location.reload(); // Reload to show updated status
      } else {
        alert(`Error: ${data.error || 'Failed to update status'}`);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('An error occurred while updating the status');
    });
  }
</script>
{% endblock %}
