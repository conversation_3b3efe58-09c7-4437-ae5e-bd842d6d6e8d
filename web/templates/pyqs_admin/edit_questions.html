{% extends "base.html" %}

{% block title %}Edit Questions{% endblock %}

{% block content %}
<!-- KaTeX CSS and JavaScript -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/13.0.1/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.4.0/highlight.min.js"></script>
<!-- CKEditor 4 with Image Support -->
<script src="https://cdn.ckeditor.com/4.18.0/standard-all/ckeditor.js"></script>

<style>
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
    }

    .questions-list {
        display: block;
    }

    .question-edit {
        display: none;
    }

    .question-card {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 10px;
        margin-bottom: 20px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .question-card:hover {
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }

    .question-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f0f0f0;
    }

    .question-number {
        background: #667eea;
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 14px;
    }

    .edit-btn {
        background: #28a745;
        color: white;
        border: none;
        padding: 8px 20px;
        border-radius: 20px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s ease;
        margin-left: auto;
        display: block;
    }

    .edit-btn:hover {
        background: #218838;
        transform: scale(1.05);
    }

    .direction-section {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .direction-title {
        font-weight: bold;
        color: #000;
        margin-bottom: 10px;
        font-size: 16px;
    }

    .direction-text {
        color: #000;
        line-height: 1.6;
    }

    .question-options-section {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .question-title {
        color: #000;
        margin-bottom: 15px;
    }

    .question-text {
        color: #000;
        line-height: 1.6;
        margin-bottom: 20px;
        padding: 10px;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 5px;
    }

    .options-container {
        margin-top: 15px;
    }

    .options-title {
        font-weight: bold;
        color: #000;
        margin-bottom: 10px;
        font-size: 14px;
    }

    .option-item {
        margin-bottom: 8px;
        padding: 8px 12px;
        background: white;
        border-radius: 5px;
        border: 1px solid #dee2e6;
        color: #000;
    }

    .answer-marks-section {
        display: flex;
        gap: 20px;
        margin-top: 15px;
    }

    .answer-info, .marks-info {
        flex: 1;
        padding: 10px;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
    }

    .answer-info strong, .marks-info strong {
        color: #000;
    }

    .loading {
        text-align: center;
        padding: 50px;
        font-size: 18px;
        color: #666;
    }

    .error {
        background: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
        border: 1px solid #f5c6cb;
    }

    .back-btn {
        background: #6c757d;
        color: white;
        border: none;
        padding: 10px 25px;
        border-radius: 25px;
        cursor: pointer;
        font-size: 16px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }

    .back-btn:hover {
        background: #5a6268;
        transform: scale(1.05);
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
        color: #333;
    }

    .form-group input, .form-group textarea {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 14px;
        box-sizing: border-box;
    }

    .form-group textarea {
        min-height: 100px;
        resize: vertical;
    }

    .save-btn {
        background: #007bff;
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        cursor: pointer;
        font-size: 16px;
        margin-right: 10px;
        transition: all 0.3s ease;
    }

    .save-btn:hover {
        background: #0056b3;
        transform: scale(1.05);
    }

    .options-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-bottom: 20px;
    }

    .marks-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    /* Ensure proper container sizing */
    .question-card {
        overflow: hidden;
    }

    .form-group {
        overflow: hidden;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .options-grid {
            grid-template-columns: 1fr;
        }

        .marks-grid {
            grid-template-columns: 1fr;
        }

        .answer-marks-section {
            flex-direction: column;
        }
    }

    .cke_editable {
        min-height: 150px;
    }

    .cke {
        width: 100% !important;
    }

    .cke_contents {
        width: 100% !important;
        box-sizing: border-box !important;
    }

    .math-helper {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 15px;
        font-size: 12px;
        color: #666;
    }

    .math-helper h4 {
        margin: 0 0 8px 0;
        color: #333;
        font-size: 13px;
    }

    .math-helper code {
        background: #e9ecef;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
    }
    pre{
        white-space: break-spaces !important;
        line-height: 1.6 !important;
    }
    .katex-display>.katex{
        text-align: justify !important;
    }

    /* Image resize styling for CKEditor 4 */
    .cke_editable img {
        max-width: 100%;
        height: auto;
    }

    /* CKEditor 4 dialog styling */
    .cke_dialog {
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid #ccc;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* Upload progress indicator */
    .cke_upload_progress {
        background: #007bff;
        height: 2px;
        border-radius: 1px;
    }

    .queWrap {
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .ques {
        color: #000;
        margin-bottom: 15px;
        line-height: 1.6;
    }

    .optNo {
        display: block;
        width: 70%;
        padding: 10px 15px;
        margin-bottom: 10px;
        text-align: left;
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 5px;
        transition: background 0.3s ease;
        color: #000;
        line-height: 1.6;
    }

    .answerwrapItem {
        background: #e8f5e8;
        border: 1px solid #c3e6c3;
        border-radius: 5px;
        padding: 10px 15px;
        margin: 10px 0;
        color: #000;
        line-height: 1.6;
        width: 70%;
    }

    .ansTxt {
        font-weight: bold;
        color: #333;
        display: block;
    }
</style>

<div class="container">
    <div class="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1>Edit Questions</h1>
                <p>Manage and edit extracted questions from the document</p>
            </div>
            <button id="backToDocumentsBtn" class="back-btn" style="margin: 0;">
                ← Back to Documents
            </button>
        </div>
    </div>

    <!-- Language Selection Section -->
    <div id="languageSection" class="question-card" style="margin-bottom: 30px;">
        <div class="question-header">
            <h3 style="margin: 0; color: #333; font-size: 1.1rem;">Question Paper Languages</h3>
            <div style="display: flex; gap: 10px;">
                <button id="switchLanguageBtn" class="edit-btn" style="background: #28a745; display: none;">Switch Language</button>
                <button id="updateLanguagesBtn" class="edit-btn" style="background: #007bff;">Update Languages</button>
            </div>
        </div>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
            <div class="form-group" style="margin-bottom: 0;">
                <label for="documentLanguage1" style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">Select Language 1:</label>
                <select id="documentLanguage1" class="form-control" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px;">
                    <option value="">Select Language 1</option>
                    <option value="English">English</option>
                    <option value="Hindi">Hindi</option>
                    <option value="Kannada">Kannada</option>
                    <option value="Marathi">Marathi</option>
                    <option value="Telugu">Telugu</option>
                </select>
            </div>
            <div class="form-group" style="margin-bottom: 0;">
                <label for="documentLanguage2" style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">Select Language 2:</label>
                <select id="documentLanguage2" class="form-control" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px;">
                    <option value="">Select Language 2</option>
                    <option value="English">English</option>
                    <option value="Hindi">Hindi</option>
                    <option value="Kannada">Kannada</option>
                    <option value="Marathi">Marathi</option>
                    <option value="Telugu">Telugu</option>
                </select>
            </div>
        </div>
        <div id="currentLanguageDisplay" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; display: none;">
            <strong>Currently showing: </strong><span id="currentLanguageText"></span>
        </div>
    </div>

    <!-- Questions List View -->
    <div id="questionsListView" class="questions-list">
        <!-- Selection Controls - Sticky -->
        <div id="selectionControls" class="question-card sticky-controls" style="margin-bottom: 20px; background: #f8f9fa; position: sticky; top: 0; z-index: 100; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="display: flex; align-items: center; gap: 15px;">
                    <label style="display: flex; align-items: center; gap: 8px; font-weight: bold; color: #333; margin: 0;">
                        <input type="checkbox" id="selectAllQuestions" style="transform: scale(1.2);">
                        Select All Questions
                    </label>
                    <span id="selectedCount" style="color: #666; font-size: 14px;">0 selected</span>
                </div>
                <div style="display: flex; gap: 10px;">
                    <button id="fixIssuesBtn" class="edit-btn" style="background: #dc3545; display: none;">
                        Fix Issues
                    </button>
                    <button id="generateSolutionsBtn" class="edit-btn" style="background: #8b5cf6; display: none;">
                        Generate Solutions
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content Area with Navigation -->
        <div class="questions-main-content">

            <!-- Questions Content -->
            <div class="questions-content">
                <div id="loadingMessage" class="loading">
                    Loading questions...
                </div>
                <div id="errorMessage" class="error" style="display: none;"></div>
                <div id="questionsContainer"></div>
            </div>

            <!-- Question Navigation Sidebar -->
            <div id="questionNavigation" class="question-navigation">
                <div class="nav-header">
                    <h4>Questions</h4>
                </div>
                <div id="questionNavList" class="nav-list">
                    <!-- Navigation items will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Question Edit View -->
    <div id="questionEditView" class="question-edit">
        <button id="backToListBtn" class="back-btn">← Back to Questions List</button>

        <div class="question-card">

            <form id="editQuestionForm">
                <input type="hidden" id="editQuestionId" name="questionId">

                <div class="math-helper">
                    <h4>Math Formula Help:</h4>
                    <p>For math formulas, use LaTeX syntax: <code>$x^2$</code> for inline math, <code>$$\frac{a}{b}$$</code> for display math, or <code>\( \)</code> and <code>\[ \]</code> delimiters.</p>
                </div>

                <div class="form-group">
                    <label for="editDirection">Direction (if applicable):</label>
                    <textarea id="editDirection" name="direction" placeholder="Enter directions for this question (optional)"></textarea>
                </div>

                <div class="form-group">
                    <label for="editQuestion">Question:</label>
                    <textarea id="editQuestion" name="question" placeholder="Enter the question text"></textarea>
                </div>

                <div class="form-group">
                    <label for="editQuestionType">Question Type:</label>
                    <input type="text" id="editQuestionType" name="question_type" placeholder="e.g., MCQ">
                </div>

                <div class="options-grid">
                    <div class="form-group">
                        <label for="editOption1">Option 1:</label>
                        <textarea id="editOption1" name="option1" placeholder="Enter option 1"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editOption2">Option 2:</label>
                        <textarea id="editOption2" name="option2" placeholder="Enter option 2"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editOption3">Option 3:</label>
                        <textarea id="editOption3" name="option3" placeholder="Enter option 3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editOption4">Option 4:</label>
                        <textarea id="editOption4" name="option4" placeholder="Enter option 4"></textarea>
                    </div>
                </div>

                <div class="form-group">
                    <label for="editOption5">Option 5 (if applicable):</label>
                    <textarea id="editOption5" name="option5" placeholder="Enter option 5 (optional)"></textarea>
                </div>

                <div class="form-group">
                    <label for="editAnswer">Answer:</label>
                    <input type="text" id="editAnswer" name="answer" placeholder="Enter the correct answer">
                </div>

                <div class="marks-grid">
                    <div class="form-group">
                        <label for="editMarks">Marks:</label>
                        <input type="number" id="editMarks" name="marks" step="0.1" placeholder="Enter marks">
                    </div>
                    <div class="form-group">
                        <label for="editNegativeMarks">Negative Marks:</label>
                        <input type="number" id="editNegativeMarks" name="negative_mark" step="0.1" placeholder="Enter negative marks">
                    </div>
                </div>

                <div class="form-group">
                    <label for="editTopic">Topic:</label>
                    <input type="text" id="editTopic" name="topic" placeholder="Enter topic">
                </div>

                <div class="form-group">
                    <label for="editSubtopic">Subtopic:</label>
                    <input type="text" id="editSubtopic" name="subtopic" placeholder="Enter subtopic">
                </div>

                <div class="form-group">
                    <label for="editSolution">Solution:</label>
                    <textarea id="editSolution" name="solution" placeholder="Enter the solution explanation (optional)"></textarea>
                </div>

                <button type="submit" class="save-btn">Save Changes</button>
                <button type="button" id="cancelEditBtn" class="back-btn">Cancel</button>
            </form>
        </div>
    </div>

    <!-- Fix Issues Modal -->
    <div id="fixIssuesModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; min-width: 450px; max-width: 600px;">
            <h3 style="margin: 0 0 20px 0; color: #333;">Fix Issues</h3>

            <!-- Selected Questions Display -->
            <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;">
                <h4 style="margin: 0 0 10px 0; color: #333; font-size: 14px;">Selected Questions:</h4>
                <div id="selectedQuestionsList" style="color: #666; font-size: 13px; line-height: 1.4;">
                    <!-- Question numbers will be populated here -->
                </div>
            </div>

            <p style="color: #666; margin-bottom: 20px;">Select the issues you want to fix for the selected questions:</p>

            <div style="margin-bottom: 20px;">
                <label style="display: flex; align-items: center; gap: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; cursor: pointer;">
                    <input type="checkbox" id="fixFormulasOption" checked style="transform: scale(1.2);">
                    <span style="font-weight: bold;">Fix Formulas</span>
                </label>
                <p style="font-size: 12px; color: #666; margin: 5px 0 0 35px;">Fix LaTeX formula expressions and ensure proper math formatting</p>
            </div>

            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                <button id="cancelFixIssues" class="back-btn" style="margin: 0;">Cancel</button>
                <button id="confirmFixIssues" class="save-btn" style="margin: 0;">Fix Selected Issues</button>
            </div>
        </div>
    </div>

    <!-- Fix Progress Modal -->
    <div id="fixProgressModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1001;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; min-width: 400px; text-align: center;">
            <h3 style="margin: 0 0 20px 0; color: #333;">Fixing Issues</h3>
            <div style="margin-bottom: 20px;">
                <div style="width: 50px; height: 50px; border: 4px solid #f3f3f3; border-top: 4px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 15px;"></div>
                <p id="fixProgressText" style="color: #666; margin: 0;">Processing questions...</p>
            </div>
        </div>
    </div>

    <!-- Generate Solutions Modal -->
    <div id="generateSolutionsModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; min-width: 450px; max-width: 600px;">
            <h3 style="margin: 0 0 20px 0; color: #333;">Generate Solutions</h3>

            <!-- Selected Questions Display -->
            <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;">
                <h4 style="margin: 0 0 10px 0; color: #333; font-size: 14px;">Selected Questions:</h4>
                <div id="selectedQuestionsListSolutions" style="color: #666; font-size: 13px; line-height: 1.4;">
                    <!-- Question numbers will be populated here -->
                </div>
            </div>

            <p style="color: #666; margin-bottom: 20px;">Generate solutions for the selected questions:</p>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 12px; font-weight: 500; font-size: 0.875rem; color: #374151;">Dual Language:</label>
                <div style="display: flex; gap: 16px;">
                    <label style="display: flex; align-items: center; gap: 8px; font-size: 0.875rem; color: #374151; cursor: pointer;">
                        <input type="radio" name="solutions-dual-language" value="false" checked style="transform: scale(1.2);">
                        <span>No</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 8px; font-size: 0.875rem; color: #374151; cursor: pointer;">
                        <input type="radio" name="solutions-dual-language" value="true" style="transform: scale(1.2);">
                        <span>Yes</span>
                    </label>
                </div>
                <div style="margin-top: 8px; font-size: 0.75rem; color: #6b7280;">Enable if the document contains questions in dual languages</div>
            </div>

            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                <button id="cancelGenerateSolutions" class="back-btn" style="margin: 0;">Cancel</button>
                <button id="confirmGenerateSolutions" class="save-btn" style="margin: 0;">Generate Solutions</button>
            </div>
        </div>
    </div>

    <!-- Generate Solutions Progress Modal -->
    <div id="generateSolutionsProgressModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1001;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; min-width: 400px; text-align: center;">
            <h3 style="margin: 0 0 20px 0; color: #333;">Generating Solutions</h3>
            <div style="margin-bottom: 20px;">
                <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #8b5cf6; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>
            </div>
            <p id="generateSolutionsProgressText" style="color: #666; margin: 0;">Processing selected questions...</p>
        </div>
    </div>
</div>

<style>
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.question-checkbox {
    transform: scale(1.2);
    margin-right: 10px;
}

.question-card.selected {
    border-color: #007bff;
    box-shadow: 0 4px 20px rgba(0,123,255,0.15);
}

.sticky-controls {
    position: sticky !important;
    top: 0 !important;
    z-index: 100 !important;
    background: #f8f9fa !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    border-bottom: 2px solid #dee2e6 !important;
}

.sticky-controls:hover {
    transform: none !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
}

#selectedQuestionsList {
    max-height: 100px;
    overflow-y: auto;
    word-wrap: break-word;
}

#selectedQuestionsList::-webkit-scrollbar {
    width: 6px;
}

#selectedQuestionsList::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#selectedQuestionsList::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#selectedQuestionsList::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Question Navigation Styles */
.questions-main-content {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.question-navigation {
    position: sticky;
    top: 120px; /* Account for sticky controls */
    min-width: 200px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    max-height: calc(100vh - 140px);
    overflow-y: auto;
    z-index: 50;
}

.nav-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
    border-radius: 10px 10px 0 0;
}

.nav-header h4 {
    margin: 0;
    color: #333;
    font-size: 16px;
    font-weight: bold;
}

.nav-list {
    padding: 10px;
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
}

.nav-question-btn {
    width: 100%;
    aspect-ratio: 1;
    border: 2px solid #e0e0e0;
    background: white;
    color: #667eea;
    font-weight: bold;
    font-size: 12px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 30px;
}

.nav-question-btn:hover {
    background: #667eea;
    color: white;
    transform: scale(1.05);
}

.nav-question-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.questions-content {
    flex: 1;
    min-width: 0; /* Allows flex item to shrink */
}
.katex-display>.katex{
    white-space: break-spaces !important;
}
/* Responsive adjustments */
@media (max-width: 1024px) {
    .questions-main-content {
        flex-direction: column;
    }

    .question-navigation {
        position: relative;
        top: 0;
        width: 100%;
        max-height: 200px;
        order: -1;
    }

    .nav-list {
        grid-template-columns: repeat(10, 1fr);
    }
}

@media (max-width: 768px) {
    .nav-list {
        grid-template-columns: repeat(8, 1fr);
    }
}
</style>

{% endblock %}

{% block scripts %}
<script>
// Get the base URL and document ID from the current location
const BASE_URL = window.location.origin;
const DOCUMENT_ID = {{ document_id }};
const EXAM_ID = {{ exam_id if exam_id else 'null' }};

// Global variables
let questions = [];
let currentQuestionIndex = -1;
let directionEditor, questionEditor, option1Editor, option2Editor, option3Editor, option4Editor, option5Editor, solutionEditor;
let examId = EXAM_ID;
let currentLanguage = 'language1'; // Track current language display (language1 or language2)
let documentLanguages = { language1: '', language2: '' }; // Store document languages
let selectedQuestions = new Set(); // Track selected question IDs

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    loadQuestions();
    loadDocumentLanguages();
    initializeEventListeners();
});

// Initialize event listeners
function initializeEventListeners() {
    document.getElementById('backToListBtn').addEventListener('click', showQuestionsList);
    document.getElementById('cancelEditBtn').addEventListener('click', showQuestionsList);
    document.getElementById('editQuestionForm').addEventListener('submit', handleSaveQuestion);
    document.getElementById('backToDocumentsBtn').addEventListener('click', goBackToDocuments);
    document.getElementById('updateLanguagesBtn').addEventListener('click', updateDocumentLanguages);
    document.getElementById('switchLanguageBtn').addEventListener('click', switchLanguage);

    // Selection and fix issues event listeners
    document.getElementById('selectAllQuestions').addEventListener('change', handleSelectAll);
    document.getElementById('fixIssuesBtn').addEventListener('click', showFixIssuesModal);
    document.getElementById('cancelFixIssues').addEventListener('click', hideFixIssuesModal);
    document.getElementById('confirmFixIssues').addEventListener('click', handleFixIssues);

    // Generate solutions event listeners
    document.getElementById('generateSolutionsBtn').addEventListener('click', showGenerateSolutionsModal);
    document.getElementById('cancelGenerateSolutions').addEventListener('click', hideGenerateSolutionsModal);
    document.getElementById('confirmGenerateSolutions').addEventListener('click', handleGenerateSolutions);
}

// Load questions from API
async function loadQuestions() {
    try {
        const response = await fetch(`${BASE_URL}/pyqs_admin/api/documents/${DOCUMENT_ID}/questions`);
        const result = await response.json();

        if (result.success) {
            questions = result.questions;
            displayQuestions();
        } else {
            showError('Failed to load questions: ' + result.error);
        }
    } catch (error) {
        console.error('Error loading questions:', error);
        showError('Error loading questions: ' + error.message);
    }
}

// Navigate back to documents page
function goBackToDocuments() {
    if (examId && examId !== null) {
        window.location.href = `${BASE_URL}/pyqs_admin/exams/${examId}/documents`;
    } else {
        // Fallback: go to exams list
        window.location.href = `${BASE_URL}/pyqs_admin/exams`;
    }
}

// Load document languages from API
async function loadDocumentLanguages() {
    try {
        const response = await fetch(`${BASE_URL}/pyqs_admin/api/documents/${DOCUMENT_ID}/languages`);
        const result = await response.json();

        if (result.success) {
            // Store document languages
            documentLanguages.language1 = result.language1 || '';
            documentLanguages.language2 = result.language2 || '';

            // Pre-select the languages if they exist
            if (result.language1) {
                document.getElementById('documentLanguage1').value = result.language1;
            }
            if (result.language2) {
                document.getElementById('documentLanguage2').value = result.language2;
            }

            // Show switch language button if both languages are available
            checkAndShowLanguageSwitch();
        } else {
            console.error('Failed to load document languages:', result.error);
        }
    } catch (error) {
        console.error('Error loading document languages:', error);
    }
}

// Check and show language switch button if both languages are available
function checkAndShowLanguageSwitch() {
    const switchBtn = document.getElementById('switchLanguageBtn');
    const currentLanguageDisplay = document.getElementById('currentLanguageDisplay');
    const currentLanguageText = document.getElementById('currentLanguageText');

    if (documentLanguages.language1 && documentLanguages.language1.trim() !== '' &&
        documentLanguages.language2 && documentLanguages.language2.trim() !== '') {
        switchBtn.style.display = 'inline-block';
        currentLanguageDisplay.style.display = 'block';
        currentLanguageText.textContent = documentLanguages.language1;
    } else {
        switchBtn.style.display = 'none';
        currentLanguageDisplay.style.display = 'none';
    }
}

// Switch between languages
function switchLanguage() {
    if (currentLanguage === 'language1') {
        currentLanguage = 'language2';
        document.getElementById('currentLanguageText').textContent = documentLanguages.language2;
    } else {
        currentLanguage = 'language1';
        document.getElementById('currentLanguageText').textContent = documentLanguages.language1;
    }

    // Refresh the questions display to show the new language
    displayQuestions();
}

// Split text by ~~ delimiter and return the appropriate part based on current language
function getLanguageText(text) {
    if (!text || typeof text !== 'string') {
        return text || '';
    }

    const parts = text.split('~~');
    if (parts.length >= 2) {
        // If we have dual language content
        if (currentLanguage === 'language1') {
            return parts[0].trim();
        } else {
            return parts[1].trim();
        }
    }

    // If no ~~ delimiter found, return the original text
    return text;
}

// Update document languages
async function updateDocumentLanguages() {
    const language1 = document.getElementById('documentLanguage1').value;
    const language2 = document.getElementById('documentLanguage2').value;

    const requestData = {
        language1: language1,
        language2: language2
    };

    try {
        const response = await fetch(`${BASE_URL}/pyqs_admin/api/documents/${DOCUMENT_ID}/languages`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        const result = await response.json();

        if (result.success) {
            alert('Question paper languages updated successfully!');
            // Update stored languages and check switch button visibility
            documentLanguages.language1 = language1;
            documentLanguages.language2 = language2;
            checkAndShowLanguageSwitch();
        } else {
            alert('Error updating languages: ' + result.error);
        }
    } catch (error) {
        console.error('Error updating question paper languages:', error);
        alert('Error updating languages: ' + error.message);
    }
}

// Display questions in the list view
function displayQuestions() {
    const loadingMessage = document.getElementById('loadingMessage');
    const questionsContainer = document.getElementById('questionsContainer');
    const questionNavList = document.getElementById('questionNavList');

    loadingMessage.style.display = 'none';

    if (questions.length === 0) {
        questionsContainer.innerHTML = '<div class="error">No questions found for this document.</div>';
        questionNavList.innerHTML = '';
        return;
    }

    let html = '';
    let navHtml = '';

    questions.forEach((question, index) => {
        const qCount = index + 1;
        html += createQuestionCard(question, index);

        // Create navigation button
        navHtml += `<button class="nav-question-btn" onclick="scrollToQuestion(${qCount})" data-question="${qCount}">${qCount}</button>`;
    });

    questionsContainer.innerHTML = html;
    questionNavList.innerHTML = navHtml;

    // Math rendering and markdown parsing are now handled within createQuestionCard function

    // Set up intersection observer for active question highlighting
    setupQuestionObserver();
}

// Helper function to extract text based on language (equivalent to extractText function)
function extractText(text, language) {
    return getLanguageText(text);
}

// Create HTML for a question card
function createQuestionCard(question, index) {
    const qCount = index + 1;
    let questionHTML = "";

    // Header with checkbox and action buttons
    questionHTML += `<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 2px solid #f0f0f0;">
        <div style="display: flex; align-items: center; gap: 10px;">
            <input type="checkbox" class="question-checkbox" data-question-id="${question.id}" data-question-number="${qCount}" onchange="handleQuestionSelection(${question.id}, this.checked)">
            <span class="question-number">Q${qCount}</span>
        </div>
        <button class="edit-btn" onclick="editQuestion(${index})">Edit Question</button>
    </div>`;
    // Add directions if present
    if (question.direction && question.direction.directions) {
        const directionText = getLanguageText(question.direction.directions);
        questionHTML += `
            <div class="direction-section">
                <div class="direction-title">Directions:</div>
                <pre class="direction-text">${directionText}</pre>
            </div>
        `;
    }

    // Start the queWrap structure
    questionHTML += "<div class='queWrap'>";
    questionHTML += "<p class='ques'>Q" + qCount + ". " + extractText(question.question, parseInt(currentLanguage === 'language1' ? 1 : 2)) + "</p>";
    questionHTML += "<p class='optNo'>A. " + extractText(question.option1, parseInt(currentLanguage === 'language1' ? 1 : 2)) + "</p>";
    questionHTML += "<p class='optNo'>B. " + extractText(question.option2, parseInt(currentLanguage === 'language1' ? 1 : 2)) + "</p>";
    questionHTML += "<p class='optNo'>C. " + extractText(question.option3, parseInt(currentLanguage === 'language1' ? 1 : 2)) + "</p>";
    questionHTML += "<p class='optNo'>D. " + extractText(question.option4, parseInt(currentLanguage === 'language1' ? 1 : 2)) + "</p>";

    if (question.option5) {
        questionHTML += "<p class='optNo'>E. " + extractText(question.option5, parseInt(currentLanguage === 'language1' ? 1 : 2)) + "</p>";
    }

    // Answer display logic based on answer1, answer2, answer3, answer4, answer5 fields
    if (question.answer1 && question.answer1 === "Yes") {
        questionHTML += "<p class='answerwrapItem'><span class='ansTxt'>Answer:</span><strong style='font-weight: bold !important;'>(A) </strong> " + question.option1 + "</p>";
    } else if (question.answer2 && question.answer2 === "Yes") {
        questionHTML += "<p class='answerwrapItem'><span class='ansTxt'>Answer:</span> <strong style='font-weight: bold !important;'> (B) </strong>" + question.option2 + "</p>";
    } else if (question.answer3 && question.answer3 === "Yes") {
        questionHTML += "<p class='answerwrapItem'><span class='ansTxt'>Answer:</span> <strong style='font-weight: bold !important;'> (C) </strong>" + question.option3 + "</p>";
    } else if (question.answer4 && question.answer4 === "Yes") {
        questionHTML += "<p class='answerwrapItem'><span class='ansTxt'>Answer:</span> <strong style='font-weight: bold !important;'> (D) </strong>" + question.option4 + "</p>";
    } else if (question.answer5 && question.answer5 === "Yes") {
        questionHTML += "<p class='answerwrapItem'><span class='ansTxt'>Answer:</span> <strong style='font-weight: bold !important;'> (E) </strong>" + question.option5 + "</p>";
    } else if (question.answer) {
        // Fallback to the answer field if answer1-5 are not available
        questionHTML += "<p class='answerwrapItem'><span class='ansTxt'>Answer:</span> <strong style='font-weight: bold !important;'>" + question.answer.toUpperCase() + "</strong></p>";
    }

    // Difficulty level display
    if (question.difficultyLevel || question.difficulty_level) {
        questionHTML += "<p><span class='ansTxt'>Difficulty:</span> " + (question.difficultyLevel || question.difficulty_level) + "</p>";
    }

    // Explanation display
    if (question.answerDescription || question.solution) {
        const explanationText = extractText(question.answerDescription || question.solution, parseInt(currentLanguage === 'language1' ? 1 : 2));
        questionHTML += "<p class='expwrapItem'><span class='ansTxt'>Explanation:</span> " + explanationText + "</p>";
    }

    // Create temporary element for math rendering
    const tempEl = document.createElement("div");
    tempEl.innerHTML = questionHTML;

    // Render math in the temporary element
    renderMathInElement(tempEl, {
        delimiters: [
            { left: "\\(", right: "\\)", display: false },
            { left: "\\[", right: "\\]", display: true }
        ]
    });

    // Parse markdown and return final HTML
    questionHTML = marked.parse(tempEl.innerHTML);

    return `<div class="question-card" id="question-${qCount}">${questionHTML}</div>`;
}

// Show error message
function showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    const loadingMessage = document.getElementById('loadingMessage');

    loadingMessage.style.display = 'none';
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
}

// Edit question function
function editQuestion(index) {
    currentQuestionIndex = index;
    const question = questions[index];

    // Populate form fields
    document.getElementById('editQuestionId').value = question.id;
    document.getElementById('editQuestionType').value = question.question_type || '';
    document.getElementById('editAnswer').value = question.answer || '';
    document.getElementById('editMarks').value = question.marks || '';
    document.getElementById('editNegativeMarks').value = question.negative_mark || '';
    document.getElementById('editTopic').value = question.topic || '';
    document.getElementById('editSubtopic').value = question.subtopic || '';

    // Initialize CKEditor for question and options
    initializeCKEditors(question);

    // Show edit view
    showQuestionEdit();
}

// CKEditor 4 Upload Configuration
function getCKEditor4Config(editorType = 'question') {
    const questionNumber = currentQuestionIndex >= 0 ? currentQuestionIndex + 1 : 1;

    return {
            // Simplified toolbar for better usability
        toolbarGroups: [
            { name: 'document', groups: [ 'mode', 'document', 'doctools' ] },
            { name: 'basicstyles', groups: [ 'basicstyles' ] },
            { name: 'clipboard', groups: [ 'clipboard', 'undo' ] },
            { name: 'paragraph', groups: [ 'list', 'indent', 'blocks', 'align', 'bidi', 'paragraph' ] },
            '/',
            { name: 'insert', groups: [ 'insert' ] },

            { name: 'styles', groups: [ 'styles' ] },
            { name: 'colors', groups: [ 'colors' ] },
            { name: 'tools', groups: [ 'tools' ] },
            { name: 'others', groups: [ 'others' ] },
            { name: 'about', groups: [ 'about' ] },
            { name: 'links', groups: [ 'links' ] }
        ],

        // Height configuration
        height: 150,

        // File browser upload URL with custom parameters
        filebrowserUploadUrl: `${BASE_URL}/pyqs_admin/upload-image?document_id=${DOCUMENT_ID}&question_number=${questionNumber}&editor_type=${editorType}`,
        filebrowserUploadMethod: 'form',

        // Image upload URL (for drag & drop and paste)
        imageUploadUrl: `${BASE_URL}/pyqs_admin/upload-image?document_id=${DOCUMENT_ID}&question_number=${questionNumber}&editor_type=${editorType}`,
        imageUploadMethod: 'form',

        // Enable image upload plugins
        extraPlugins: 'mathjax,uploadimage,image2',

        // Remove plugins we don't need
        removePlugins: 'elementspath',

        // Image configuration
        image2_alignClasses: ['image-align-left', 'image-align-center', 'image-align-right'],
        image2_disableResizer: false,
        mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML',
        mathJax: {
            extensions: ['tex2jax.js'],
            jax: ['input/TeX', 'output/HTML-CSS'],
            tex2jax: {
                inlineMath: [['$', '$']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true
            }
        }
    }
}

// Initialize CKEditor 4 instances
function initializeCKEditors(question) {
    // Destroy existing editors if they exist
    destroyCKEditors();

    // Wait a bit for DOM to be ready
    setTimeout(() => {

        // Initialize direction editor
        try {
            directionEditor = CKEDITOR.replace('editDirection', getCKEditor4Config('direction'));
            directionEditor.on('instanceReady', function() {
                const directionText = question.direction && question.direction.directions ?
                    question.direction.directions : '';
                directionEditor.setData(directionText);
                console.log('Direction editor initialized successfully');
            });
        } catch (error) {
            console.error('Error initializing direction editor:', error);
        }

        // Initialize question editor
        try {
            questionEditor = CKEDITOR.replace('editQuestion', getCKEditor4Config('question'));
            questionEditor.on('instanceReady', function() {
                questionEditor.setData(question.question || '');
                console.log('Question editor initialized successfully');
            });
        } catch (error) {
            console.error('Error initializing question editor:', error);
        }

        // Initialize option editors
        const optionFields = ['editOption1', 'editOption2', 'editOption3', 'editOption4', 'editOption5'];
        const optionKeys = ['option1', 'option2', 'option3', 'option4', 'option5'];
        const optionTypes = ['option1', 'option2', 'option3', 'option4', 'option5'];

        optionFields.forEach((fieldId, index) => {
            try {
                const editor = CKEDITOR.replace(fieldId, getCKEditor4Config(optionTypes[index]));
                editor.on('instanceReady', function() {
                    editor.setData(question[optionKeys[index]] || '');
                    console.log(`${fieldId} editor initialized successfully for ${optionTypes[index]}`);
                });

                // Store editor references
                if (index === 0) option1Editor = editor;
                else if (index === 1) option2Editor = editor;
                else if (index === 2) option3Editor = editor;
                else if (index === 3) option4Editor = editor;
                else if (index === 4) option5Editor = editor;
            } catch (error) {
                console.error(`Error initializing ${fieldId} editor:`, error);
            }
        });

        // Initialize solution editor
        try {
            solutionEditor = CKEDITOR.replace('editSolution', getCKEditor4Config('solution'));
            solutionEditor.on('instanceReady', function() {
                solutionEditor.setData(question.solution || '');
                console.log('Solution editor initialized successfully');
            });
        } catch (error) {
            console.error('Error initializing solution editor:', error);
        }
    }, 100); // Small delay to ensure DOM is ready
}

// Destroy CKEditor 4 instances
function destroyCKEditors() {
    if (directionEditor && directionEditor.destroy) {
        directionEditor.destroy();
        directionEditor = null;
    }
    if (questionEditor && questionEditor.destroy) {
        questionEditor.destroy();
        questionEditor = null;
    }
    if (option1Editor && option1Editor.destroy) {
        option1Editor.destroy();
        option1Editor = null;
    }
    if (option2Editor && option2Editor.destroy) {
        option2Editor.destroy();
        option2Editor = null;
    }
    if (option3Editor && option3Editor.destroy) {
        option3Editor.destroy();
        option3Editor = null;
    }
    if (option4Editor && option4Editor.destroy) {
        option4Editor.destroy();
        option4Editor = null;
    }
    if (option5Editor && option5Editor.destroy) {
        option5Editor.destroy();
        option5Editor = null;
    }
    if (solutionEditor && solutionEditor.destroy) {
        solutionEditor.destroy();
        solutionEditor = null;
    }

    // Also destroy any remaining CKEditor instances
    for (let name in CKEDITOR.instances) {
        CKEDITOR.instances[name].destroy();
    }
}

// Show questions list view
function showQuestionsList() {
    destroyCKEditors();
    document.getElementById('questionsListView').style.display = 'block';
    document.getElementById('questionEditView').style.display = 'none';
}

// Show question edit view
function showQuestionEdit() {
    document.getElementById('questionsListView').style.display = 'none';
    document.getElementById('questionEditView').style.display = 'block';
}

// Handle save question
async function handleSaveQuestion(event) {
    event.preventDefault();

    if (currentQuestionIndex === -1) {
        alert('No question selected for editing');
        return;
    }

    const questionId = document.getElementById('editQuestionId').value;

    // Collect form data
    const formData = {
        directions: directionEditor && directionEditor.getData ? directionEditor.getData() : '',
        question: questionEditor && questionEditor.getData ? questionEditor.getData() : '',
        question_type: document.getElementById('editQuestionType').value,
        option1: option1Editor && option1Editor.getData ? option1Editor.getData() : '',
        option2: option2Editor && option2Editor.getData ? option2Editor.getData() : '',
        option3: option3Editor && option3Editor.getData ? option3Editor.getData() : '',
        option4: option4Editor && option4Editor.getData ? option4Editor.getData() : '',
        option5: option5Editor && option5Editor.getData ? option5Editor.getData() : '',
        answer: document.getElementById('editAnswer').value,
        marks: document.getElementById('editMarks').value,
        negative_mark: document.getElementById('editNegativeMarks').value,
        topic: document.getElementById('editTopic').value,
        subtopic: document.getElementById('editSubtopic').value,
        solution: solutionEditor && solutionEditor.getData ? solutionEditor.getData() : ''
    };

    try {
        const response = await fetch(`${BASE_URL}/pyqs_admin/solutions/${questionId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            alert('Question updated successfully!');

            // Update the question in our local array
            questions[currentQuestionIndex] = {
                ...questions[currentQuestionIndex],
                ...formData,
                // Update direction structure to match expected format
                direction: formData.directions ? { directions: formData.directions } : null
            };

            // Go back to list view and refresh display
            showQuestionsList();
            displayQuestions();
        } else {
            alert('Error updating question: ' + result.error);
        }
    } catch (error) {
        console.error('Error saving question:', error);
        alert('Error saving question: ' + error.message);
    }
}

// Question selection functions
function handleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllQuestions');
    const questionCheckboxes = document.querySelectorAll('.question-checkbox');

    questionCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
        const questionId = parseInt(checkbox.dataset.questionId);
        if (selectAllCheckbox.checked) {
            selectedQuestions.add(questionId);
        } else {
            selectedQuestions.delete(questionId);
        }
    });

    updateSelectionUI();
}

function handleQuestionSelection(questionId, isSelected) {
    if (isSelected) {
        selectedQuestions.add(questionId);
    } else {
        selectedQuestions.delete(questionId);
    }

    // Update select all checkbox state
    const questionCheckboxes = document.querySelectorAll('.question-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllQuestions');
    const allSelected = Array.from(questionCheckboxes).every(cb => cb.checked);
    const noneSelected = Array.from(questionCheckboxes).every(cb => !cb.checked);

    if (allSelected) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else if (noneSelected) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }

    updateSelectionUI();
}

function updateSelectionUI() {
    const selectedCount = selectedQuestions.size;
    const selectedCountElement = document.getElementById('selectedCount');
    const fixIssuesBtn = document.getElementById('fixIssuesBtn');
    const generateSolutionsBtn = document.getElementById('generateSolutionsBtn');

    selectedCountElement.textContent = `${selectedCount} selected`;

    if (selectedCount > 0) {
        fixIssuesBtn.style.display = 'inline-block';
        generateSolutionsBtn.style.display = 'inline-block';
    } else {
        fixIssuesBtn.style.display = 'none';
        generateSolutionsBtn.style.display = 'none';
    }
}

// Fix issues modal functions
function showFixIssuesModal() {
    if (selectedQuestions.size === 0) {
        alert('Please select at least one question to fix issues.');
        return;
    }

    // Populate selected questions list
    populateSelectedQuestionsList();

    document.getElementById('fixIssuesModal').style.display = 'block';
}

function populateSelectedQuestionsList() {
    const selectedQuestionsList = document.getElementById('selectedQuestionsList');

    if (selectedQuestions.size === 0) {
        selectedQuestionsList.innerHTML = 'No questions selected';
        return;
    }

    // Get question numbers for selected IDs using data attributes
    const selectedQuestionNumbers = [];
    const questionCheckboxes = document.querySelectorAll('.question-checkbox:checked');

    questionCheckboxes.forEach(checkbox => {
        const questionNumber = parseInt(checkbox.dataset.questionNumber);
        if (questionNumber) {
            selectedQuestionNumbers.push(questionNumber);
        }
    });

    // Sort the question numbers
    selectedQuestionNumbers.sort((a, b) => a - b);

    // Create display text
    selectedQuestionsList.innerHTML = `<strong>Questions:</strong> ${selectedQuestionNumbers.join(', ')}`;
}

function hideFixIssuesModal() {
    document.getElementById('fixIssuesModal').style.display = 'none';
}

function showFixProgressModal() {
    document.getElementById('fixProgressModal').style.display = 'block';
}

function hideFixProgressModal() {
    document.getElementById('fixProgressModal').style.display = 'none';
}

async function handleFixIssues() {
    const fixFormulasChecked = document.getElementById('fixFormulasOption').checked;

    if (!fixFormulasChecked) {
        alert('Please select at least one issue to fix.');
        return;
    }

    const issueTypes = [];
    if (fixFormulasChecked) {
        issueTypes.push('fix_formulas');
    }

    const questionIds = Array.from(selectedQuestions);

    try {
        hideFixIssuesModal();
        showFixProgressModal();

        const response = await fetch(`${BASE_URL}/pyqs_admin/api/questions/fix-issues`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                question_ids: questionIds,
                issue_types: issueTypes
            })
        });

        const result = await response.json();

        if (result.success) {
            // Start polling for task status
            pollFixTaskStatus(result.task_id);
        } else {
            hideFixProgressModal();
            alert('Error starting fix process: ' + result.error);
        }
    } catch (error) {
        hideFixProgressModal();
        console.error('Error fixing issues:', error);
        alert('Error fixing issues: ' + error.message);
    }
}

async function pollFixTaskStatus(taskId) {
    try {
        const response = await fetch(`${BASE_URL}/pyqs_admin/api/task-status/${taskId}`);
        const result = await response.json();

        if (result.status === 'COMPLETED') {
            hideFixProgressModal();
            alert('Issues fixed successfully! Refreshing questions...');

            // Clear selections and reload questions
            selectedQuestions.clear();
            document.getElementById('selectAllQuestions').checked = false;
            document.getElementById('selectAllQuestions').indeterminate = false;
            window.location.reload()
        } else if (result.status === 'FAILED') {
            hideFixProgressModal();
            alert('Fix process failed: ' + (result.error || 'Unknown error'));
        } else {
            // Still in progress, continue polling
            document.getElementById('fixProgressText').textContent =
                `Processing ${selectedQuestions.size} questions...`;
            setTimeout(() => pollFixTaskStatus(taskId), 2000);
        }
    } catch (error) {
        hideFixProgressModal();
        console.error('Error checking task status:', error);
        alert('Error checking fix progress: ' + error.message);
    }
}

// Generate Solutions modal functions
function showGenerateSolutionsModal() {
    if (selectedQuestions.size === 0) {
        alert('Please select at least one question to generate solutions.');
        return;
    }

    // Populate selected questions list
    populateSelectedQuestionsListSolutions();

    document.getElementById('generateSolutionsModal').style.display = 'block';
}

function populateSelectedQuestionsListSolutions() {
    const selectedQuestionsList = document.getElementById('selectedQuestionsListSolutions');

    if (selectedQuestions.size === 0) {
        selectedQuestionsList.innerHTML = 'No questions selected';
        return;
    }

    // Get question numbers for selected IDs using data attributes
    const selectedQuestionNumbers = [];
    const questionCheckboxes = document.querySelectorAll('.question-checkbox:checked');

    questionCheckboxes.forEach(checkbox => {
        const questionNumber = parseInt(checkbox.dataset.questionNumber);
        if (questionNumber) {
            selectedQuestionNumbers.push(questionNumber);
        }
    });

    // Sort the question numbers
    selectedQuestionNumbers.sort((a, b) => a - b);

    // Create display text
    selectedQuestionsList.innerHTML = `<strong>Questions:</strong> ${selectedQuestionNumbers.join(', ')}`;
}

function hideGenerateSolutionsModal() {
    document.getElementById('generateSolutionsModal').style.display = 'none';
}

function showGenerateSolutionsProgressModal() {
    document.getElementById('generateSolutionsProgressModal').style.display = 'block';
}

function hideGenerateSolutionsProgressModal() {
    document.getElementById('generateSolutionsProgressModal').style.display = 'none';
}

async function handleGenerateSolutions() {
    const dualLanguage = document.querySelector('input[name="solutions-dual-language"]:checked').value === 'true';
    const questionIds = Array.from(selectedQuestions);

    try {
        hideGenerateSolutionsModal();
        showGenerateSolutionsProgressModal();

        const response = await fetch(`${BASE_URL}/pyqs_admin/api/questions/generate-solutions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                question_ids: questionIds,
                dual_language: dualLanguage
            })
        });

        const result = await response.json();

        if (result.success) {
            // Start polling for task status
            pollGenerateSolutionsTaskStatus(result.task_id);
        } else {
            hideGenerateSolutionsProgressModal();
            alert('Error starting solution generation: ' + result.error);
        }
    } catch (error) {
        hideGenerateSolutionsProgressModal();
        console.error('Error generating solutions:', error);
        alert('Error generating solutions: ' + error.message);
    }
}

async function pollGenerateSolutionsTaskStatus(taskId) {
    try {
        const response = await fetch(`${BASE_URL}/pyqs_admin/api/task-status/${taskId}`);
        const result = await response.json();

        if (result.status === 'COMPLETED') {
            hideGenerateSolutionsProgressModal();
            alert('Solutions generated successfully! Refreshing questions...');

            // Clear selections and reload questions
            selectedQuestions.clear();
            document.getElementById('selectAllQuestions').checked = false;
            document.getElementById('selectAllQuestions').indeterminate = false;
            window.location.reload()
        } else if (result.status === 'FAILED') {
            hideGenerateSolutionsProgressModal();
            alert('Solution generation failed: ' + (result.error || 'Unknown error'));
        } else {
            // Still in progress, continue polling
            document.getElementById('generateSolutionsProgressText').textContent =
                `Processing ${selectedQuestions.size} questions...`;
            setTimeout(() => pollGenerateSolutionsTaskStatus(taskId), 2000);
        }
    } catch (error) {
        hideGenerateSolutionsProgressModal();
        console.error('Error checking task status:', error);
        alert('Error checking solution generation progress: ' + error.message);
    }
}

// Question Navigation Functions
function scrollToQuestion(questionNumber) {
    const questionElement = document.getElementById(`question-${questionNumber}`);
    if (questionElement) {
        // Smooth scroll to the question with offset for sticky controls
        const offsetTop = questionElement.offsetTop - 140; // Account for sticky controls height
        window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
        });

        // Update active state
        updateActiveNavButton(questionNumber);
    }
}

function updateActiveNavButton(activeQuestionNumber) {
    // Remove active class from all nav buttons
    const navButtons = document.querySelectorAll('.nav-question-btn');
    navButtons.forEach(btn => btn.classList.remove('active'));

    // Add active class to the current button
    const activeButton = document.querySelector(`[data-question="${activeQuestionNumber}"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }
}

function setupQuestionObserver() {
    // Create intersection observer to highlight active question in navigation
    const observerOptions = {
        root: null,
        rootMargin: '-150px 0px -50% 0px', // Trigger when question is in upper portion of viewport
        threshold: 0.1
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const questionId = entry.target.id;
                const questionNumber = questionId.replace('question-', '');
                updateActiveNavButton(parseInt(questionNumber));
            }
        });
    }, observerOptions);

    // Observe all question cards
    const questionCards = document.querySelectorAll('.question-card[id^="question-"]');
    questionCards.forEach(card => observer.observe(card));
}
</script>
{% endblock %}