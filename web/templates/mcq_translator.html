{% extends "base.html" %}

{% block title %}MCQ Translator{% endblock %}

{% block content %}

<style>
/* Loader Styles */
.loader-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.loader {
    width: 60px;
    height: 60px;
    border: 6px solid #f3f3f3;
    border-top: 6px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loader-text {
    flex: 1;
    max-width: 400px;
}

#loaderTitle {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

#loaderSubtitle {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.progress-bar-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 12px;
    font-weight: bold;
    color: #4CAF50;
    min-width: 35px;
}

.translation-results {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.metadata {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.translation-text {
    white-space: pre-wrap;
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 5px;
    max-height: 500px;
    overflow-y: auto;
}

.buttons-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.view-button, .download-button {
    background-color: #4CAF50;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.view-button:hover, .download-button:hover {
    background-color: #45a049;
}

.view-button:disabled, .download-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    opacity: 0.6;
}

.view-button:disabled:hover, .download-button:disabled:hover {
    background-color: #cccccc;
}

.download-button {
    background-color: #2196F3;
}

.download-button:hover {
    background-color: #1976D2;
}

.link-container {
    margin-top: 15px;
    padding: 15px;
    background-color: #f0f0f0;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.link-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.link-input {
    width: calc(100% - 100px);
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-right: 10px;
    font-family: monospace;
    font-size: 14px;
}

.copy-button {
    background-color: #FF9800;
    color: white;
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.copy-button:hover {
    background-color: #F57C00;
}
select[name='sourceLanguage'],
    select[name='destinationLanguage']{
        padding: 0.6rem !important;
        background: #f4f4f4;
        border-radius: 6px !important;
        border: 1px solid #ccc !important;
        font-size: 1rem !important;
        margin-bottom: 1.2rem !important;
    }

/* Progress Log Styles */
#logContainer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
    margin-top: 20px;
}

#logContainer h3 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

#logContainer h3::before {
    content: "📋";
    font-size: 20px;
}

#timer {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white !important;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 2px 8px rgba(0,123,255,0.3); }
    50% { box-shadow: 0 2px 15px rgba(0,123,255,0.5); }
    100% { box-shadow: 0 2px 8px rgba(0,123,255,0.3); }
}

.progress-log {
    background: #ffffff;
    border-radius: 8px;
    padding: 15px;
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.progress-log::-webkit-scrollbar {
    width: 8px;
}

.progress-log::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 4px;
}

.progress-log::-webkit-scrollbar-thumb {
    background: #c1c8cd;
    border-radius: 4px;
}

.progress-log::-webkit-scrollbar-thumb:hover {
    background: #a8b2ba;
}

.log-entry {
    padding: 8px 12px;
    margin-bottom: 6px;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.4;
    position: relative;
    animation: slideIn 0.3s ease-out;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.log-entry:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}

/* Different styles for different types of log entries */
.log-entry:contains("Error") {
    background-color: #f8d7da;
    color: #721c24;
    border-left-color: #dc3545;
}

.log-entry:contains("completed") {
    background-color: #d4edda;
    color: #155724;
    border-left-color: #28a745;
}

.log-entry:contains("Starting") {
    background-color: #d1ecf1;
    color: #0c5460;
    border-left-color: #17a2b8;
}

.log-entry:contains("Status:") {
    background-color: #fff3cd;
    color: #856404;
    border-left-color: #ffc107;
}

.log-entry:contains("Task started") {
    background-color: #e2e3e5;
    color: #383d41;
    border-left-color: #6c757d;
    font-weight: 500;
}

.log-entry::before {
    content: "•";
    color: #6c757d;
    margin-right: 8px;
    font-weight: bold;
}

/* Add timestamp to log entries */
.log-entry::after {
    content: attr(data-time);
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 11px;
    color: #6c757d;
    opacity: 0.7;
}

/* Empty state for progress log */
.progress-log:empty::before {
    content: "No logs yet...";
    display: block;
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
}
</style>
<div class="test-container">
    <div class="test-card">
        <h1>MCQ Translator</h1>
        <p class="test-description">Translate MCQs from one language to another</p>

        <form id="translatorForm" class="login-form" enctype="multipart/form-data">
            <label for="pdfFile">Upload PDF File</label>
            <input type="file" id="pdfFile" name="pdfFile" accept=".pdf" required>

            <label for="totalQuestions">Total Questions</label>
            <input type="number" id="totalQuestions" name="totalQuestions" min="1" value="10" required>

            <label for="sourceLanguage">Source Language</label>
            <select id="sourceLanguage" name="sourceLanguage" required>
                <option value="English">English</option>
                <option value="Hindi">Hindi</option>
                <option value="Tamil">Tamil</option>
                <option value="Assamese">Assamese</option>
                <option value="Telugu">Telugu</option>
                <option value="Kannada">Kannada</option>
                <option value="Malayalam">Malayalam</option>
                <option value="Marathi">Marathi</option>
                <option value="Bengali">Bengali</option>
                <option value="Gujarati">Gujarati</option>
                <option value="Urdu">Urdu</option>
            </select>

            <label for="destinationLanguage">Destination Language</label>
            <select id="destinationLanguage" name="destinationLanguage" required>
                <option value="Hindi">Hindi</option>
                <option value="English">English</option>
                <option value="Tamil">Tamil</option>
                <option value="Assamese">Assamese</option>
                <option value="Telugu">Telugu</option>
                <option value="Kannada">Kannada</option>
                <option value="Malayalam">Malayalam</option>
                <option value="Marathi">Marathi</option>
                <option value="Bengali">Bengali</option>
                <option value="Gujarati">Gujarati</option>
                <option value="Urdu">Urdu</option>
            </select>

            <button type="submit">Translate MCQ</button>
        </form>
        <div id="loaderContainer" style="display: none; margin-top: 20px;">
            <div class="loader-wrapper">
                <div class="loader"></div>
                <div class="loader-text">
                    <div id="loaderTitle">Processing Translation...</div>
                    <div id="loaderSubtitle">Please wait while we process your PDF file</div>
                    <div class="progress-bar-container" style="display: none;">
                        <div class="progress-bar">
                            <div id="progressBarFill" class="progress-bar-fill"></div>
                        </div>
                        <div id="progressText" class="progress-text">0%</div>
                    </div>
                </div>
            </div>
        </div>

        <div id="logContainer" style="display: none; margin-top: 20px;">
            <h3>Progress Log: <span id="timer" style="color: #007bff; font-weight: bold;">00:00</span></h3>
            <div id="progressLog" class="progress-log"></div>
        </div>

        <div id="resultContainer" style="display: none; margin-top: 20px;">
            <h3>Translation Results:</h3>
            <div id="translationResults" class="translation-results"></div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('translatorForm');
    const loaderContainer = document.getElementById('loaderContainer');
    const loaderTitle = document.getElementById('loaderTitle');
    const loaderSubtitle = document.getElementById('loaderSubtitle');
    const progressBarFill = document.getElementById('progressBarFill');
    const progressText = document.getElementById('progressText');
    const logContainer = document.getElementById('logContainer');
    const progressLog = document.getElementById('progressLog');
    const resultContainer = document.getElementById('resultContainer');
    const translationResults = document.getElementById('translationResults');

    function addLogEntry(message, type = 'info') {
        const entry = document.createElement('div');
        entry.className = 'log-entry';

        // Add timestamp
        const now = new Date();
        const timestamp = now.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        entry.setAttribute('data-time', timestamp);

        // Set message content
        entry.textContent = message;

        // Add specific styling based on message content or type
        if (message.toLowerCase().includes('error') || type === 'error') {
            entry.classList.add('log-error');
            entry.style.backgroundColor = '#f8d7da';
            entry.style.color = '#721c24';
            entry.style.borderLeftColor = '#dc3545';
        } else if (message.toLowerCase().includes('completed') || message.toLowerCase().includes('success') || type === 'success') {
            entry.classList.add('log-success');
            entry.style.backgroundColor = '#d4edda';
            entry.style.color = '#155724';
            entry.style.borderLeftColor = '#28a745';
        } else if (message.toLowerCase().includes('starting') || message.toLowerCase().includes('initializing') || type === 'start') {
            entry.classList.add('log-start');
            entry.style.backgroundColor = '#d1ecf1';
            entry.style.color = '#0c5460';
            entry.style.borderLeftColor = '#17a2b8';
        } else if (message.toLowerCase().includes('status:') || message.toLowerCase().includes('checking') || type === 'status') {
            entry.classList.add('log-status');
            entry.style.backgroundColor = '#fff3cd';
            entry.style.color = '#856404';
            entry.style.borderLeftColor = '#ffc107';
        } else if (message.toLowerCase().includes('task started') || type === 'task') {
            entry.classList.add('log-task');
            entry.style.backgroundColor = '#e2e3e5';
            entry.style.color = '#383d41';
            entry.style.borderLeftColor = '#6c757d';
            entry.style.fontWeight = '500';
        }

        // Add entry with animation
        entry.style.opacity = '0';
        entry.style.transform = 'translateX(-10px)';
        progressLog.appendChild(entry);

        // Trigger animation
        setTimeout(() => {
            entry.style.opacity = '1';
            entry.style.transform = 'translateX(0)';
            entry.style.transition = 'all 0.3s ease-out';
        }, 10);

        // Auto-scroll to bottom
        progressLog.scrollTop = progressLog.scrollHeight;

        // Add a subtle sound effect for important messages (optional)
        if (type === 'error' || type === 'success') {
            // You can add a subtle beep sound here if desired
        }
    }

    function updateLoader(title, subtitle, progress) {
        loaderTitle.textContent = title;
        loaderSubtitle.textContent = subtitle;
        progressBarFill.style.width = progress + '%';
        progressText.textContent = progress + '%';
    }

    function showLoader() {
        loaderContainer.style.display = 'block';
        updateLoader('Initializing Translation...', 'Preparing to process your PDF file', 0);
    }

    function hideLoader() {
        loaderContainer.style.display = 'none';
    }

    // Timer functionality
    let startTime = null;
    let timerInterval = null;

    function startTimer() {
        startTime = new Date();
        timerInterval = setInterval(updateTimer, 1000);
    }

    function updateTimer() {
        if (startTime) {
            const now = new Date();
            const elapsed = Math.floor((now - startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            const timerElement = document.getElementById('timer');
            if (timerElement) {
                timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }
    }

    function stopTimer() {
        if (timerInterval) {
            clearInterval(timerInterval);
            timerInterval = null;
        }
        if (startTime) {
            const now = new Date();
            const elapsed = Math.floor((now - startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        return '00:00';
    }

    function resetTimer() {
        stopTimer();
        startTime = null;
        const timerElement = document.getElementById('timer');
        if (timerElement) {
            timerElement.textContent = '00:00';
        }
    }

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Clear previous results
        progressLog.innerHTML = '';
        translationResults.innerHTML = '';
        resultContainer.style.display = 'none';

        // Reset timer and show loader
        resetTimer();
        showLoader();
        logContainer.style.display = 'block';

        // Start the timer
        startTimer();

        // Get form values
        const pdfFile = document.getElementById('pdfFile').files[0];
        const totalQuestions = document.getElementById('totalQuestions').value;
        const sourceLanguage = document.getElementById('sourceLanguage').value;
        const destinationLanguage = document.getElementById('destinationLanguage').value;

        // Validate inputs
        if (!pdfFile || !totalQuestions || !sourceLanguage || !destinationLanguage) {
            addLogEntry('Error: All fields are required', 'error');
            hideLoader();
            return;
        }

        if (sourceLanguage === destinationLanguage) {
            addLogEntry('Error: Source and destination languages must be different', 'error');
            hideLoader();
            return;
        }

        addLogEntry(`Starting translation for file: ${pdfFile.name}`, 'start');
        addLogEntry(`Source language: ${sourceLanguage}, Destination language: ${destinationLanguage}`, 'info');

        try {
            // Create FormData for file upload
            const formData = new FormData();
            formData.append('pdfFile', pdfFile);
            formData.append('total_questions', totalQuestions);
            formData.append('source_language', sourceLanguage);
            formData.append('destination_language', destinationLanguage);
            formData.append('username', 'web_user');

            // Call the API to start translation
            const response = await fetch('/api/mcq-translator-file', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.status === 'started') {
                // Task started successfully, begin polling
                addLogEntry(`Task started with ID: ${data.task_id}`, 'task');
                addLogEntry('Polling for status updates...', 'status');

                // Start polling for status
                pollTaskStatus(data.task_id);
            } else {
                // Stop timer and get final time
                const finalTime = stopTimer();

                // Hide loader
                hideLoader();

                addLogEntry(`Error: ${data.message} (Failed after ${finalTime})`, 'error');
            }

        } catch (error) {
            // Stop timer and get final time
            const finalTime = stopTimer();

            // Hide loader
            hideLoader();

            addLogEntry(`Error: ${error.message} (Failed after ${finalTime})`, 'error');
        }
    });

    // Polling function for task status
    async function pollTaskStatus(taskId) {
        const pollInterval = 5000; // 5 seconds
        let pollCount = 0;
        const maxPolls = 360; // Maximum 30 minutes (360 * 5 seconds)

        const poll = async () => {
            try {
                pollCount++;
                addLogEntry(`Checking status...`, 'status');

                const response = await fetch('/api/mcq-translator-file/status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        task_id: taskId
                    })
                });

                const statusResult = await response.json();

                if (statusResult.status === 'COMPLETED') {
                    // Task completed successfully
                    const finalTime = stopTimer();
                    hideLoader();

                    addLogEntry(`Translation completed successfully! (Completed in ${finalTime})`, 'success');

                    // Display results from the task result
                    if (statusResult.result) {
                        displayTranslationResults(statusResult.result, finalTime);
                    } else {
                        addLogEntry('Task completed but no result data available', 'error');
                    }

                    return; // Stop polling

                } else if (statusResult.status === 'FAILED') {
                    // Task failed
                    const finalTime = stopTimer();
                    hideLoader();

                    addLogEntry(`Translation failed: ${statusResult.message} (Failed after ${finalTime})`, 'error');
                    return; // Stop polling

                } else if (statusResult.status === 'not_found') {
                    // Task not found
                    const finalTime = stopTimer();
                    hideLoader();

                    addLogEntry(`Task not found: ${statusResult.message} (Failed after ${finalTime})`, 'error');
                    return; // Stop polling

                } else {
                    // Task still in progress
                    addLogEntry(`Status: ${statusResult.status} - ${statusResult.message || 'Processing...'}`, 'status');

                    // Continue polling if we haven't exceeded max polls
                    if (pollCount < maxPolls) {
                        setTimeout(poll, pollInterval);
                    } else {
                        // Timeout reached
                        const finalTime = stopTimer();
                        hideLoader();
                        addLogEntry(`Translation timed out after ${maxPolls * pollInterval / 1000 / 60} minutes (Timed out after ${finalTime})`, 'error');
                    }
                }

            } catch (error) {
                addLogEntry(`Error checking status: ${error.message}`, 'error');

                // Continue polling on error (might be temporary network issue)
                if (pollCount < maxPolls) {
                    setTimeout(poll, pollInterval);
                } else {
                    const finalTime = stopTimer();
                    hideLoader();
                    addLogEntry(`Polling failed after ${maxPolls} attempts (Failed after ${finalTime})`, 'error');
                }
            }
        };

        // Start polling
        setTimeout(poll, pollInterval);
    }

    // Function to display translation results
    function displayTranslationResults(result, completionTime) {
        // Show the translation results container
        resultContainer.style.display = 'block';

        // Create buttons container
        const buttonsContainer = document.createElement('div');
        buttonsContainer.className = 'buttons-container';

        // Create a button to view the translated content
        const viewButton = document.createElement('button');
        viewButton.textContent = 'View Translated Content';
        viewButton.className = 'view-button';
        viewButton.addEventListener('click', async function() {
            try {
                // Show loading state on button
                const originalText = viewButton.textContent;
                viewButton.textContent = 'Loading...';
                viewButton.disabled = true;

                addLogEntry('Fetching translated content...', 'status');

                const contentResponse = await fetch(`/api/get-translated-mcq-content/${result.translation_id}`);
                const contentData = await contentResponse.json();

                if (contentData.status === 'success') {
                    // Clear previous content
                    translationResults.innerHTML = '';
                    translationResults.appendChild(buttonsContainer);

                    // Add the translated text in a pre element
                    const textPre = document.createElement('pre');
                    textPre.className = 'translation-text';
                    textPre.textContent = contentData.content;
                    translationResults.appendChild(textPre);

                    addLogEntry('Translated content loaded successfully', 'success');
                } else {
                    addLogEntry(`Error fetching translated content: ${contentData.message}`, 'error');
                }
            } catch (error) {
                addLogEntry(`Error: ${error.message}`, 'error');
            } finally {
                // Restore button state
                viewButton.textContent = originalText;
                viewButton.disabled = false;
            }
        });

        // Create download button
        const downloadButton = document.createElement('button');
        downloadButton.textContent = 'Download Translated File';
        downloadButton.className = 'download-button';
        downloadButton.addEventListener('click', function() {
            // Show loading state on button
            const originalText = downloadButton.textContent;
            downloadButton.textContent = 'Downloading...';
            downloadButton.disabled = true;

            const downloadUrl = `/api/download-translated-mcq/${result.translation_id}`;
            window.open(downloadUrl, '_blank');
            addLogEntry('Download started...', 'info');

            // Restore button state after a short delay
            setTimeout(() => {
                downloadButton.textContent = originalText;
                downloadButton.disabled = false;
            }, 2000);
        });

        // Create link display and copy functionality
        const linkContainer = document.createElement('div');
        linkContainer.className = 'link-container';

        const linkLabel = document.createElement('label');
        linkLabel.textContent = 'Download Link:';

        const linkInput = document.createElement('input');
        linkInput.type = 'text';
        linkInput.className = 'link-input';
        linkInput.value = `${window.location.origin}/api/download-translated-mcq/${result.translation_id}`;
        linkInput.readOnly = true;

        const copyButton = document.createElement('button');
        copyButton.textContent = 'Copy Link';
        copyButton.className = 'copy-button';
        copyButton.addEventListener('click', function() {
            linkInput.select();
            document.execCommand('copy');
            copyButton.textContent = 'Copied!';
            addLogEntry('Download link copied to clipboard', 'success');
            setTimeout(() => {
                copyButton.textContent = 'Copy Link';
            }, 2000);
        });

        // Add elements to containers
        buttonsContainer.appendChild(viewButton);
        buttonsContainer.appendChild(downloadButton);

        linkContainer.appendChild(linkLabel);
        linkContainer.appendChild(linkInput);
        linkContainer.appendChild(copyButton);

        translationResults.appendChild(buttonsContainer);
        translationResults.appendChild(linkContainer);

        // Add completion summary with better formatting
        addLogEntry(`🎉 Translation completed in ${completionTime}`, 'success');
        addLogEntry(`📋 Translation ID: ${result.translation_id}`, 'info');
        addLogEntry(`📄 File: ${result.filename}`, 'info');
        addLogEntry(`❓ Questions: ${result.total_questions}`, 'info');
        addLogEntry(`🌐 ${result.source_language} → ${result.destination_language}`, 'info');
    }
});
</script>

{% endblock %}