"""
API routes for the PYQs Admin module.
"""

import os
import math
import logging
import shutil
import asyncio
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Optional, List
from utils.s3_utils import read_file_from_s3

from fastapi import APIRouter, Depends, Request, HTTPException, Form, Query, File, UploadFile, Body
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse, FileResponse, StreamingResponse
from fastapi.templating import Jinja2Templates
from auth.dependencies import require_login
from auth.rbac import require_roles
from db_config.pyqs_admin_db import (
    get_levels_by_site_id,
    get_syllabus_by_level,
    get_grades_by_syllabus,
    get_grades_wonderpublish_style,
    get_subjects_by_syllabus,
    get_all_exams,
    get_exam_by_id,
    create_exam,
    update_exam,
    get_exam_documents,
    get_exam_document_by_id,
    create_exam_document,
    update_exam_document_content,
    update_exam_document_extracted_content_status,
    update_exam_document_languages,
    remove_exam_document,
    get_exam_solutions,
    create_exam_solution,
    update_exam_solution,
    delete_exam_solution,
    delete_all_exam_solutions,
    create_direction,
    get_exam_questions_with_directions
)
from agents.syllabus_processor import SyllabusProcessor
from models.pyqs_admin_models import (
    ExamCreate,
    ExamUpdate,
    ExamDocumentCreate,
    ExamSolutionCreate,
    ExamSolutionUpdate,
    ExtractContentRequest,
    ExtractQuestionsRequest,
    GenerateSolutionsRequest,
    FixQuestionsIssuesRequest,
    GenerateSelectedQuestionsSolutionsRequest
)
from utils.pyqs_utils import (
    upload_question_paper_to_s3,
    extract_text_from_pdf,
    extract_html_from_pdf,
    extract_questions_from_pdf
)
from utils.pdf_extraction_utils import get_extraction_json_path
import config

logger = logging.getLogger(__name__)

# Initialize the router
router = APIRouter(prefix="/pyqs_admin", tags=["pyqs_admin"])

# Initialize the templates
templates = Jinja2Templates(directory="web/templates")


@router.get("/", response_class=HTMLResponse)
async def pyqs_admin_home(
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Redirect to the exam list page
    """
    if login_check:
        return login_check

    return RedirectResponse(url="/pyqs_admin/exams")


@router.get("/", response_class=RedirectResponse)
async def pyqs_admin_root(
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Redirect to the exams list
    """
    if login_check:
        return login_check

    return RedirectResponse(url="/pyqs_admin/exams")


@router.get("/exams", response_class=HTMLResponse)
async def exam_list(
    request: Request,
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    search: str = Query(None),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the list of exams
    """
    if login_check:
        return login_check

    # Calculate offset
    offset = (page - 1) * limit

    # Get exams
    exams, total_count = get_all_exams(limit=limit, offset=offset, search=search)

    # Calculate total pages
    total_pages = math.ceil(total_count / limit)

    return templates.TemplateResponse(
        "pyqs_admin/exam_list.html",
        {
            "request": request,
            "exams": exams,
            "total": total_count,
            "page": page,
            "limit": limit,
            "pages": total_pages,
            "search": search or ""
        }
    )


@router.get("/exams/create", response_class=HTMLResponse)
async def create_exam_form(
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the form for creating a new exam
    """
    if login_check:
        return login_check

    # Get levels for initial dropdown
    levels = get_levels_by_site_id()

    return templates.TemplateResponse(
        "pyqs_admin/create_exam.html",
        {
            "request": request,
            "levels": levels,
            "is_edit": False
        }
    )


@router.post("/exams/create", response_class=HTMLResponse)
async def create_exam_submit(
    request: Request,
    exam_name: str = Form(...),
    level: str = Form(...),
    syllabus: str = Form(...),
    grade: str = Form(...),
    subject: str = Form(...),
    syllabus_text: str = Form(""),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Handle the form submission for creating a new exam
    """
    if login_check:
        return login_check

    try:
        # Create exam data
        exam_data = {
            "exam_name": exam_name,
            "level": level,
            "syllabus": syllabus,
            "grade": grade,
            "subject": subject,
            "syllabus_text": syllabus_text
        }

        # Get username from session
        username = request.session.get("user", {}).get("username", "system")

        # Create exam
        create_exam(exam_data, username)

        # Redirect to exam list
        return RedirectResponse(url="/pyqs_admin/exams", status_code=303)
    except ValueError as e:
        # Handle duplicate exam error
        logger.warning(f"Duplicate exam error: {e}")

        # Get levels for dropdown
        levels = get_levels_by_site_id()

        # Get syllabi for the selected level
        syllabi = get_syllabus_by_level(level)

        # Get grades for the selected syllabus
        grades = get_grades_by_syllabus(syllabus)

        # Get subjects for the selected syllabus
        subjects = get_subjects_by_syllabus(syllabus)

        return templates.TemplateResponse(
            "pyqs_admin/create_exam.html",
            {
                "request": request,
                "levels": levels,
                "syllabi": syllabi,
                "grades": grades,
                "subjects": subjects,
                "error": str(e),
                "exam_name": exam_name,
                "level": level,
                "syllabus": syllabus,
                "grade": grade,
                "subject": subject,
                "syllabus_text": syllabus_text,
                "is_edit": False
            },
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error creating exam: {e}")

        # Get levels for dropdown
        levels = get_levels_by_site_id()

        return templates.TemplateResponse(
            "pyqs_admin/create_exam.html",
            {
                "request": request,
                "levels": levels,
                "error": f"Error creating exam: {str(e)}",
                "exam_name": exam_name,
                "level": level,
                "syllabus": syllabus,
                "grade": grade,
                "subject": subject,
                "syllabus_text": syllabus_text,
                "is_edit": False
            },
            status_code=400
        )


@router.get("/exams/edit/{exam_id}", response_class=HTMLResponse)
async def edit_exam_form(
    request: Request,
    exam_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the form for editing an exam
    """
    if login_check:
        return login_check

    # Get exam
    exam = get_exam_by_id(exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Get levels for dropdown
    levels = get_levels_by_site_id()

    # Get syllabi for the selected level
    syllabi = get_syllabus_by_level(exam["level"])

    # Get grades for the selected syllabus
    grades = get_grades_by_syllabus(exam["syllabus"])

    # Get subjects for the selected syllabus
    subjects = get_subjects_by_syllabus(exam["syllabus"])

    return templates.TemplateResponse(
        "pyqs_admin/create_exam.html",
        {
            "request": request,
            "exam": exam,
            "levels": levels,
            "syllabi": syllabi,
            "grades": grades,
            "subjects": subjects,
            "is_edit": True
        }
    )


@router.post("/exams/edit/{exam_id}", response_class=HTMLResponse)
async def edit_exam_submit(
    request: Request,
    exam_id: int,
    exam_name: str = Form(...),
    level: str = Form(...),
    syllabus: str = Form(...),
    grade: str = Form(...),
    subject: str = Form(...),
    syllabus_text: str = Form(""),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Handle the form submission for editing an exam
    """
    if login_check:
        return login_check

    try:
        # Create exam data
        exam_data = {
            "exam_name": exam_name,
            "level": level,
            "syllabus": syllabus,
            "grade": grade,
            "subject": subject,
            "syllabus_text": syllabus_text
        }

        # Update exam
        success = update_exam(exam_id, exam_data)
        if not success:
            raise HTTPException(status_code=404, detail="Exam not found")

        # Redirect to exam list
        return RedirectResponse(url="/pyqs_admin/exams", status_code=303)
    except ValueError as e:
        # Handle duplicate exam error
        logger.warning(f"Duplicate exam error: {e}")

        # Get levels for dropdown
        levels = get_levels_by_site_id()

        # Get syllabi for the selected level
        syllabi = get_syllabus_by_level(level)

        # Get grades for the selected syllabus
        grades = get_grades_by_syllabus(syllabus)

        # Get subjects for the selected syllabus
        subjects = get_subjects_by_syllabus(syllabus)

        return templates.TemplateResponse(
            "pyqs_admin/create_exam.html",
            {
                "request": request,
                "levels": levels,
                "syllabi": syllabi,
                "grades": grades,
                "subjects": subjects,
                "error": str(e),
                "exam": {
                    "id": exam_id,
                    "exam_name": exam_name,
                    "level": level,
                    "syllabus": syllabus,
                    "grade": grade,
                    "subject": subject,
                    "syllabus_text": syllabus_text
                },
                "is_edit": True
            },
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error updating exam: {e}")

        # Get levels for dropdown
        levels = get_levels_by_site_id()

        # Get syllabi for the selected level
        syllabi = get_syllabus_by_level(level)

        # Get grades for the selected syllabus
        grades = get_grades_by_syllabus(syllabus)

        # Get subjects for the selected syllabus
        subjects = get_subjects_by_syllabus(syllabus)

        return templates.TemplateResponse(
            "pyqs_admin/create_exam.html",
            {
                "request": request,
                "levels": levels,
                "syllabi": syllabi,
                "grades": grades,
                "subjects": subjects,
                "error": f"Error updating exam: {str(e)}",
                "exam": {
                    "id": exam_id,
                    "exam_name": exam_name,
                    "level": level,
                    "syllabus": syllabus,
                    "grade": grade,
                    "subject": subject,
                    "syllabus_text": syllabus_text
                },
                "is_edit": True
            },
            status_code=400
        )


# API endpoints for cascading dropdowns
@router.get("/api/levels")
async def get_levels(
    request: Request,
    site_id: int = Query(1),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get all levels for a specific site_id
    """
    if login_check:
        return login_check

    levels = get_levels_by_site_id(site_id)
    return {"levels": levels}


@router.get("/api/syllabi/{level}")
async def get_syllabi(
    request: Request,
    level: str,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get all syllabi for a specific level in wonderpublish format
    """
    if login_check:
        return login_check

    # Get site_id from session or default to 1 (not used currently)
    site_id = request.session.get("user", {}).get("siteId", 1)

    syllabi = get_syllabus_by_level(level)

    # Return in wonderpublish format
    return {
        "results": syllabi,
        "status": "OK" if syllabi else "Nothing present"
    }


@router.get("/api/grades/{syllabus}")
async def get_grades(
    request: Request,
    syllabus: str,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get all grades for a specific syllabus in wonderpublish format
    """
    if login_check:
        return login_check

    # Get site_id from session or default to 1
    site_id = request.session.get("user", {}).get("siteId", 1)

    # Call the wonderpublish style function which returns the complete response
    return get_grades_wonderpublish_style(syllabus, site_id)


@router.get("/api/subjects/{syllabus}")
async def get_subjects(
    request: Request,
    syllabus: str,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get all subjects for a specific syllabus in wonderpublish format
    """
    if login_check:
        return login_check

    subjects = get_subjects_by_syllabus(syllabus)

    # Return in wonderpublish format
    return {
        "results": subjects,
        "status": "OK" if subjects else "Nothing present"
    }


# Syllabus processing endpoints
@router.post("/api/exams/{exam_id}/syllabus-to-json")
async def convert_syllabus_to_json(
    request: Request,
    exam_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Convert syllabus text to JSON for a specific exam
    """
    if login_check:
        return login_check

    try:
        # Initialize syllabus processor
        processor = SyllabusProcessor()

        # Generate request ID for tracking
        import uuid
        request_id = str(uuid.uuid4())

        # Process syllabus to JSON
        result = processor.process_syllabus_to_json(exam_id, request_id)

        if result["status"] == "success":
            return JSONResponse(
                content={
                    "success": True,
                    "message": result["message"],
                    "request_id": request_id
                }
            )
        else:
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "error": result["message"],
                    "request_id": request_id
                }
            )

    except Exception as e:
        logger.error(f"Error in syllabus to JSON conversion: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Internal server error: {str(e)}"
            }
        )


@router.get("/api/exams/{exam_id}/syllabus-json")
async def get_syllabus_json(
    request: Request,
    exam_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get the syllabus JSON for a specific exam
    """
    if login_check:
        return login_check

    try:
        # Initialize syllabus processor
        processor = SyllabusProcessor()

        # Get syllabus JSON
        result = processor.get_syllabus_json(exam_id)

        if result["status"] == "success":
            return JSONResponse(
                content={
                    "success": True,
                    "data": result["data"]
                }
            )
        else:
            return JSONResponse(
                status_code=404,
                content={
                    "success": False,
                    "error": result["message"]
                }
            )

    except Exception as e:
        logger.error(f"Error getting syllabus JSON: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Internal server error: {str(e)}"
            }
        )


# Document management routes
@router.get("/exams/{exam_id}/documents", response_class=HTMLResponse)
async def exam_documents(
    request: Request,
    exam_id: int,
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    extracted_content_filter: str = Query(None),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the list of documents for a specific exam
    """
    if login_check:
        return login_check

    # Calculate offset
    offset = (page - 1) * limit

    # Get exam details
    exam = get_exam_by_id(exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Get documents with optional filter
    documents, total_count = get_exam_documents(exam_id, limit=limit, offset=offset, extracted_content_filter=extracted_content_filter)

    # Calculate total pages
    total_pages = math.ceil(total_count / limit)

    # Calculate pagination range (equivalent to max(1, page - 2), min(pages + 1, page + 3))
    start_page = max(1, page - 2)
    end_page = min(total_pages + 1, page + 3)
    page_range = list(range(start_page, end_page))

    return templates.TemplateResponse(
        "pyqs_admin/exam_documents.html",
        {
            "request": request,
            "exam": exam,
            "documents": documents,
            "total": total_count,
            "page": page,
            "limit": limit,
            "pages": total_pages,
            "page_range": page_range,
            "extracted_content_filter": extracted_content_filter
        }
    )


@router.post("/exams/{exam_id}/documents", response_class=HTMLResponse)
async def upload_document(
    request: Request,
    exam_id: int,
    year: int = Form(...),
    month: Optional[str] = Form(None),
    shift: Optional[str] = Form(None),
    language1: Optional[str] = Form(None),
    language2: Optional[str] = Form(None),
    file: UploadFile = File(...),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Upload a new document for a specific exam
    """
    if login_check:
        return login_check

    # Get exam details
    exam = get_exam_by_id(exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Check file type
    if not file.filename.lower().endswith(".pdf"):
        return templates.TemplateResponse(
            "pyqs_admin/exam_documents.html",
            {
                "request": request,
                "exam": exam,
                "documents": [],
                "total": 0,
                "page": 1,
                "limit": 10,
                "pages": 0,
                "page_range": [],
                "error": "Only PDF files are allowed"
            }
        )

    try:
        # Read file content
        file_content = await file.read()

        # Upload to S3
        success, s3_path = upload_question_paper_to_s3(
            file_content,
            exam_id,
            year,
            month,
            shift
        )

        if not success:
            return templates.TemplateResponse(
                "pyqs_admin/exam_documents.html",
                {
                    "request": request,
                    "exam": exam,
                    "documents": [],
                    "total": 0,
                    "page": 1,
                    "limit": 10,
                    "pages": 0,
                    "page_range": [],
                    "error": "Failed to upload file to S3"
                }
            )

        # Create document record in database
        document_data = {
            "year": year,
            "month": month,
            "shift": shift,
            "language1": language1,
            "language2": language2,
            "question_paper_path": s3_path
        }

        # Get username from session
        username = request.session.get("username", "system")

        # Create document
        document_id = create_exam_document(exam_id, document_data, username)

        # Redirect to documents list
        return RedirectResponse(
            url=f"/pyqs_admin/exams/{exam_id}/documents",
            status_code=303  # HTTP 303 See Other
        )

    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        return templates.TemplateResponse(
            "pyqs_admin/exam_documents.html",
            {
                "request": request,
                "exam": exam,
                "documents": [],
                "total": 0,
                "page": 1,
                "limit": 10,
                "pages": 0,
                "page_range": [],
                "error": f"Error uploading document: {str(e)}"
            }
        )


@router.get("/documents/{document_id}/view", response_class=HTMLResponse)
async def view_document(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    View a document - redirects to PDF endpoint
    """
    if login_check:
        return login_check

    # Redirect to the PDF endpoint which handles the actual PDF serving
    return RedirectResponse(
        url=f"/pyqs_admin/documents/{document_id}/pdf",
        status_code=302  # HTTP 302 Found (temporary redirect)
    )


@router.get("/documents/{document_id}/pdf")
async def get_pdf(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get the PDF file for a document
    """
    if login_check:
        return login_check

    # Get document details
    document = get_exam_document_by_id(document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    # Get the full S3 path
    full_s3_path = os.path.join(config.S3_MOUNT_PATH, document["question_paper_path"])

    # Read the file using the S3 utility function that handles sudo permissions
    file_content = read_file_from_s3(full_s3_path)

    if file_content is None:
        raise HTTPException(status_code=404, detail="PDF file not found or could not be read")

    # Create a temporary file to serve
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
    temp_path = temp_file.name

    try:
        # Write the content to the temporary file
        with open(temp_path, "wb") as f:
            f.write(file_content)

        # Return the file with Content-Disposition set to inline to prevent download
        response = FileResponse(
            temp_path,
            media_type="application/pdf",
            filename=os.path.basename(document["question_paper_path"])
        )

        # Set Content-Disposition header to inline to display in browser
        response.headers["Content-Disposition"] = f"inline; filename=\"{os.path.basename(document['question_paper_path'])}\""

        # Set up cleanup callback to delete the temporary file after response is sent
        async def cleanup():
            try:
                os.unlink(temp_path)
            except Exception as e:
                logger.error(f"Error cleaning up temporary file {temp_path}: {e}")

        response.background = cleanup

        return response
    except Exception as e:
        # Clean up the temporary file if an error occurs
        try:
            os.unlink(temp_path)
        except:
            pass

        logger.error(f"Error serving PDF file: {e}")
        raise HTTPException(status_code=500, detail="Error serving PDF file")


@router.post("/documents/{document_id}/extract-content")
async def extract_content(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Extract HTML content from a document
    """
    if login_check:
        return login_check

    # Get document details
    document = get_exam_document_by_id(document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    try:
        # Extract HTML content
        html_content = extract_html_from_pdf(document["question_paper_path"])
        if not html_content:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to extract HTML content"}
            )

        # Update document with extracted content
        success = update_exam_document_content(document_id, html_content)
        if not success:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to update document with extracted content"}
            )

        return JSONResponse(
            content={"success": True, "message": "Content extracted successfully"}
        )

    except Exception as e:
        logger.error(f"Error extracting content: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error extracting content: {str(e)}"}
        )


@router.get("/documents/{document_id}/view-content", response_class=HTMLResponse)
async def view_content(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    View the extracted HTML content of a document
    """
    if login_check:
        return login_check

    # Get document details
    document = get_exam_document_by_id(document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    # Check if document has extracted content
    if not document.get("extracted_content"):
        raise HTTPException(status_code=404, detail="Document has no extracted content")

    # Get exam details
    exam = get_exam_by_id(document["exam_id"])
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    return templates.TemplateResponse(
        "pyqs_admin/view_content.html",
        {
            "request": request,
            "document": document,
            "exam": exam
        }
    )


@router.post("/documents/extract-questions")
async def extract_questions(
    request: Request,
    request_data: dict = Body(...),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Extract questions from a document
    """
    if login_check:
        return login_check

    try:
        # Validate request data
        if not request_data or "total_questions" not in request_data:
            raise HTTPException(status_code=422, detail="total_questions is required")

        total_questions = request_data.get("total_questions")
        document_id = request_data.get("document_id")
        dual_language = request_data.get("dual_language", False)  # Default to False

        # Validate total_questions
        if not isinstance(total_questions, int) or total_questions < 1 or total_questions > 500:
            raise HTTPException(status_code=422, detail="total_questions must be an integer between 1 and 500")

        # Validate dual_language type
        if not isinstance(dual_language, bool):
            raise HTTPException(status_code=422, detail="dual_language must be a boolean")

        # Get document details
        document = get_exam_document_by_id(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")

        # Get the question paper path
        question_paper_path = document.get("question_paper_path")
        if not question_paper_path:
            raise HTTPException(status_code=400, detail="Document has no question paper path")

        # Import the service
        from services.document_question_extractor_service import DocumentQuestionExtractorService

        # Create service instance
        extractor_service = DocumentQuestionExtractorService()

        # Get username from session
        username = request.session.get("username", "unknown")

        # Start the extraction process (this will run in background)
        result = await extractor_service.extract_questions_from_document(
            document_id=document_id,
            question_paper_path=question_paper_path,
            total_questions=total_questions,
            username=username,
            dual_language=dual_language
        )

        if result["status"] == "error":
            return JSONResponse(
                status_code=500,
                content={"error": result["message"]}
            )

        # Return task ID for polling
        return JSONResponse(
            content={
                "success": True,
                "message": "Question extraction started",
                "task_id": result["task_id"]
            }
        )

    except Exception as e:
        logger.error(f"Error starting question extraction: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error starting question extraction: {str(e)}"}
        )


@router.post("/documents/{document_id}/generate-solutions")
async def generate_solutions(
    request: Request,
    document_id: int,
    request_data: dict = Body(None),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Generate solutions for all questions in a document
    """
    if login_check:
        return login_check

    try:
        # Extract dual_language parameter
        dual_language = False
        if request_data:
            dual_language = request_data.get("dual_language", False)

            # Validate dual_language type
            if not isinstance(dual_language, bool):
                raise HTTPException(status_code=422, detail="dual_language must be a boolean")

        # Get document details to verify it exists
        document = get_exam_document_by_id(document_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")

        # Check if document has questions
        questions = get_exam_questions_with_directions(document_id)
        if not questions:
            return JSONResponse(
                status_code=400,
                content={"error": "No questions found for this document. Please extract questions first."}
            )

        # Import the service
        from services.document_solution_generator_service import DocumentSolutionGeneratorService

        # Create service instance
        generator_service = DocumentSolutionGeneratorService()

        # Get username from session
        username = request.session.get("username", "unknown")

        # Start the solution generation process (this will run in background)
        result = await generator_service.generate_solutions_for_document(
            document_id=document_id,
            username=username,
            dual_language=dual_language
        )

        if result["status"] == "success":
            return JSONResponse(
                content={
                    "success": True,
                    "task_id": result["task_id"],
                    "message": "Solution generation started successfully"
                }
            )
        else:
            return JSONResponse(
                status_code=500,
                content={"error": result["message"]}
            )

    except Exception as e:
        logger.error(f"Error starting solution generation: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error starting solution generation: {str(e)}"}
        )


@router.get("/documents/extraction-status/{task_id}")
async def get_extraction_status(
    request: Request,
    task_id: str,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get the status of a question extraction task
    """
    if login_check:
        return login_check

    try:
        # Import the service
        from services.request_tracker_service import RequestTrackerService

        # Create service instance
        tracker_service = RequestTrackerService()

        # Get task status
        status_info = tracker_service.get_task_status(task_id)

        return JSONResponse(content=status_info)

    except Exception as e:
        logger.error(f"Error getting extraction status: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error getting extraction status: {str(e)}"}
        )


@router.get("/documents/{document_id}/edit-questions", response_class=HTMLResponse)
async def edit_questions(
    request: Request,
    document_id: int,
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Edit the extracted questions of a document
    """
    if login_check:
        return login_check

    # Get document details to get the exam_id and languages
    document = get_exam_document_by_id(document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    exam_id = document.get("content_exam_mst_id")

    return templates.TemplateResponse(
        "pyqs_admin/edit_questions.html",
        {
            "request": request,
            "document_id": document_id,
            "exam_id": exam_id,
            "document": document,
            "page": page,
            "limit": limit
        }
    )


@router.get("/api/documents/{document_id}")
async def get_document_details(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get document details including exam_id
    """
    if login_check:
        return login_check

    try:
        # Get document details
        document = get_exam_document_by_id(document_id)

        if not document:
            return JSONResponse(
                status_code=404,
                content={
                    "success": False,
                    "error": "Document not found"
                }
            )

        return JSONResponse(
            content={
                "success": True,
                "document": document
            }
        )

    except Exception as e:
        logger.error(f"Error getting document details: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Error getting document details: {str(e)}"
            }
        )


@router.get("/api/documents/{document_id}/questions")
async def get_document_questions(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get all questions for a specific document with directions included
    """
    if login_check:
        return login_check

    try:
        # Get questions with directions
        questions = get_exam_questions_with_directions(document_id)

        return JSONResponse(
            content={
                "success": True,
                "questions": questions,
                "total": len(questions)
            }
        )

    except Exception as e:
        logger.error(f"Error getting document questions: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Error getting document questions: {str(e)}"
            }
        )


@router.get("/api/documents/{document_id}/languages")
async def get_document_languages(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get the languages for a specific document
    """
    if login_check:
        return login_check

    try:
        # Get document details including languages
        document = get_exam_document_by_id(document_id)

        if not document:
            return JSONResponse(
                status_code=404,
                content={
                    "success": False,
                    "error": "Document not found"
                }
            )

        return JSONResponse(
            content={
                "success": True,
                "language1": document.get("language1", ""),
                "language2": document.get("language2", "")
            }
        )

    except Exception as e:
        logger.error(f"Error getting document languages: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Error getting document languages: {str(e)}"
            }
        )


@router.post("/api/documents/{document_id}/languages")
async def update_document_languages(
    request: Request,
    document_id: int,
    language_data: dict = Body(...),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Update the languages for a specific document
    """
    if login_check:
        return login_check

    try:
        # Get document to verify it exists
        document = get_exam_document_by_id(document_id)

        if not document:
            return JSONResponse(
                status_code=404,
                content={
                    "success": False,
                    "error": "Document not found"
                }
            )

        # Extract language data
        language1 = language_data.get("language1", "")
        language2 = language_data.get("language2", "")

        # Update document languages using raw query
        success = update_exam_document_languages(document_id, language1, language2)

        if success:
            return JSONResponse(
                content={
                    "success": True,
                    "message": "Document languages updated successfully"
                }
            )
        else:
            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "error": "Failed to update document languages"
                }
            )

    except Exception as e:
        logger.error(f"Error updating document languages: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Error updating document languages: {str(e)}"
            }
        )


@router.post("/api/questions/fix-issues")
async def fix_questions_issues(
    request: Request,
    request_data: FixQuestionsIssuesRequest,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Fix issues for selected questions using LLM
    """
    if login_check:
        return login_check

    try:
        # Extract request data
        question_ids = request_data.question_ids
        issue_types = request_data.issue_types

        # Validate input
        if not question_ids:
            return JSONResponse(
                status_code=400,
                content={"success": False, "error": "No question IDs provided"}
            )

        if not issue_types:
            return JSONResponse(
                status_code=400,
                content={"success": False, "error": "No issue types provided"}
            )

        # Validate question IDs are integers
        try:
            question_ids = [int(qid) for qid in question_ids]
        except (ValueError, TypeError):
            return JSONResponse(
                status_code=400,
                content={"success": False, "error": "Invalid question IDs format"}
            )

        # Validate issue types
        valid_issue_types = ['fix_formulas']
        invalid_types = [it for it in issue_types if it not in valid_issue_types]
        if invalid_types:
            return JSONResponse(
                status_code=400,
                content={"success": False, "error": f"Invalid issue types: {invalid_types}"}
            )

        # Import the service
        from services.question_fix_service import QuestionFixService

        # Create service instance
        fix_service = QuestionFixService()

        # Get username from session
        username = request.session.get("username", "unknown")

        # Start the fix process (this will run in background)
        result = await fix_service.fix_questions_issues(
            question_ids=question_ids,
            issue_types=issue_types,
            username=username
        )

        if result["status"] == "error":
            return JSONResponse(
                status_code=500,
                content={"success": False, "error": result["message"]}
            )

        # Return task ID for polling
        return JSONResponse(
            content={
                "success": True,
                "message": "Question fix process started",
                "task_id": result["task_id"]
            }
        )

    except Exception as e:
        logger.error(f"Error starting question fix process: {e}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": f"Error starting question fix process: {str(e)}"}
        )


@router.post("/api/questions/generate-solutions")
async def generate_solutions_for_selected_questions(
    request: Request,
    request_data: GenerateSelectedQuestionsSolutionsRequest,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Generate solutions for selected questions using LLM
    """
    if login_check:
        return login_check

    if role_check:
        return role_check

    try:
        question_ids = request_data.question_ids
        dual_language = request_data.dual_language

        # Validate input
        if not question_ids:
            return JSONResponse(
                status_code=400,
                content={"success": False, "error": "No question IDs provided"}
            )

        # Import the service
        from services.selected_questions_solution_generator_service import SelectedQuestionsSolutionGeneratorService

        # Create service instance
        generator_service = SelectedQuestionsSolutionGeneratorService()

        # Get username from session
        username = request.session.get("username", "unknown")

        # Start the solution generation process (this will run in background)
        result = await generator_service.generate_solutions_for_selected_questions(
            question_ids=question_ids,
            username=username,
            dual_language=dual_language
        )

        if result["status"] == "success":
            return JSONResponse(
                content={
                    "success": True,
                    "task_id": result["task_id"],
                    "message": "Solution generation started successfully"
                }
            )
        else:
            return JSONResponse(
                status_code=500,
                content={"success": False, "error": result["message"]}
            )

    except Exception as e:
        logger.error(f"Error starting solution generation for selected questions: {e}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": f"Error starting solution generation: {str(e)}"}
        )


@router.get("/api/task-status/{task_id}")
async def get_task_status(
    request: Request,
    task_id: str,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Get the status of any background task
    """
    if login_check:
        return login_check

    try:
        # Import the service
        from services.request_tracker_service import RequestTrackerService

        # Create service instance
        tracker_service = RequestTrackerService()

        # Get task status
        status_info = tracker_service.get_task_status(task_id)

        return JSONResponse(content=status_info)

    except Exception as e:
        logger.error(f"Error getting task status: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error getting task status: {str(e)}"}
        )


@router.post("/upload-image")
async def upload_image_for_editor(
    request: Request,
    upload: Optional[UploadFile] = File(None),
    file: Optional[UploadFile] = File(None),
    document_id: Optional[int] = Form(None),
    question_number: Optional[int] = Form(None),
    editor_type: Optional[str] = Form(None),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Upload an image for CKEditor (supports both CKEditor 4 and 5 formats)
    """
    if login_check:
        return login_check

    try:
        # Handle CKEditor 4 query parameters
        query_params = request.query_params
        ckeditor_name = query_params.get('CKEditor')
        ckeditor_func_num = query_params.get('CKEditorFuncNum')

        # Determine which file parameter was used
        uploaded_file = upload if upload else file
        if not uploaded_file:
            error_msg = "No file uploaded"
            if ckeditor_func_num:
                return HTMLResponse(
                    content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '', '{error_msg}');</script>",
                    status_code=400
                )
            else:
                return JSONResponse(
                    status_code=400,
                    content={"error": {"message": error_msg}}
                )

        # Get parameters from form data or query parameters
        if document_id is None:
            document_id = query_params.get('document_id')
            if document_id:
                document_id = int(document_id)

        if question_number is None:
            question_number = query_params.get('question_number')
            if question_number:
                question_number = int(question_number)

        if editor_type is None:
            editor_type = query_params.get('editor_type', 'question')

        # Validate required parameters
        if not document_id or document_id <= 0:
            error_msg = "Invalid document_id parameter"
            if ckeditor_func_num:
                # CKEditor 4 format response
                return HTMLResponse(
                    content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '', '{error_msg}');</script>",
                    status_code=400
                )
            else:
                # JSON response for CKEditor 5 or AJAX
                return JSONResponse(
                    status_code=400,
                    content={"error": {"message": error_msg}}
                )

        if not question_number or question_number <= 0:
            error_msg = "Invalid question_number parameter"
            if ckeditor_func_num:
                return HTMLResponse(
                    content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '', '{error_msg}');</script>",
                    status_code=400
                )
            else:
                return JSONResponse(
                    status_code=400,
                    content={"error": {"message": error_msg}}
                )

        if not editor_type or editor_type not in ['question', 'option1', 'option2', 'option3', 'option4', 'option5', 'solution', 'direction']:
            error_msg = "Invalid editor_type parameter"
            if ckeditor_func_num:
                return HTMLResponse(
                    content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '', '{error_msg}');</script>",
                    status_code=400
                )
            else:
                return JSONResponse(
                    status_code=400,
                    content={"error": {"message": error_msg}}
                )

        logger.info(f"Uploading image for document_id: {document_id}, question_number: {question_number}, editor_type: {editor_type}, ckeditor_func_num: {ckeditor_func_num}")
        logger.info(f"File details: filename={uploaded_file.filename}, content_type={uploaded_file.content_type}, size={uploaded_file.size if hasattr(uploaded_file, 'size') else 'unknown'}")

        # Validate file type
        allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
        if uploaded_file.content_type not in allowed_types:
            error_msg = "Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed."
            if ckeditor_func_num:
                return HTMLResponse(
                    content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '', '{error_msg}');</script>",
                    status_code=400
                )
            else:
                return JSONResponse(
                    status_code=400,
                    content={"error": {"message": error_msg}}
                )

        # Validate file size (max 5MB)
        max_size = 5 * 1024 * 1024  # 5MB
        file_content = await uploaded_file.read()
        if len(file_content) > max_size:
            error_msg = "File size too large. Maximum size is 5MB."
            if ckeditor_func_num:
                return HTMLResponse(
                    content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '', '{error_msg}');</script>",
                    status_code=400
                )
            else:
                return JSONResponse(
                    status_code=400,
                    content={"error": {"message": error_msg}}
                )

        # Create a temporary file
        import tempfile

        # Use the actual uploaded filename instead of custom naming convention
        # This ensures that when images are replaced, they get updated properly
        import time

        # Get the original filename and extension
        original_filename = uploaded_file.filename or "image"
        filename_parts = original_filename.rsplit('.', 1)
        if len(filename_parts) == 2:
            name_part, extension = filename_parts
            # Clean the filename to remove any problematic characters
            clean_name = "".join(c for c in name_part if c.isalnum() or c in ('-', '_', ' ')).strip()
            if not clean_name:
                clean_name = "image"
        else:
            clean_name = "".join(c for c in original_filename if c.isalnum() or c in ('-', '_', ' ')).strip()
            if not clean_name:
                clean_name = "image"
            extension = "png"  # Default extension if none found

        # Add timestamp to ensure uniqueness when replacing images
        timestamp = int(time.time())
        question_filename = f"{clean_name}_{timestamp}.{extension}"

        with tempfile.NamedTemporaryFile(delete=False, suffix=f".{extension}") as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            # Upload to S3 using existing utility
            from utils.s3_utils import upload_file_to_s3

            # Use the correct path structure: supload/pdfextracts/documents/{document_id}/main/extractedQuizImages/{actual_filename}
            s3_path = upload_file_to_s3(
                temp_file_path,
                book_id="documents",
                chapter_id=str(document_id),
                res_id="main",
                file_name=question_filename,
                is_quiz_image=True
            )

            if s3_path:
                # Build CDN path based on config.CURRENT_ENV
                import config
                current_env = getattr(config, 'CURRENT_ENV', 'qa').lower()
                if current_env == 'publish':
                    current_env = 'live'
                cdn_path = f"https://d1xcofdbxwssh7.cloudfront.net/{current_env}/"
                image_url = f"{cdn_path}{s3_path}"

                logger.info(f"Image uploaded successfully: s3_path={s3_path}, cdn_url={image_url}")

                # Return appropriate response format
                if ckeditor_func_num:
                    # CKEditor 4 format response
                    return HTMLResponse(
                        content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '{image_url}', 'Image uploaded successfully');</script>"
                    )
                else:
                    # JSON response for CKEditor 5 or AJAX
                    return JSONResponse(
                        content={
                            "fileName": question_filename,
                            "uploaded": 1,
                            "url": image_url
                        }
                    )
            else:
                error_msg = "Failed to upload image to S3"
                logger.error(f"S3 upload failed for image: {question_filename}")
                if ckeditor_func_num:
                    return HTMLResponse(
                        content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '', '{error_msg}');</script>",
                        status_code=500
                    )
                else:
                    return JSONResponse(
                        status_code=500,
                        content={"error": {"message": error_msg}}
                    )

        finally:
            # Clean up temporary file
            import os
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        logger.error(f"Error uploading image: {e}")
        error_msg = f"Error uploading image: {str(e)}"

        # Try to get ckeditor_func_num from query params for error response
        try:
            query_params = request.query_params
            ckeditor_func_num = query_params.get('CKEditorFuncNum')
            if ckeditor_func_num:
                return HTMLResponse(
                    content=f"<script>window.parent.CKEDITOR.tools.callFunction({ckeditor_func_num}, '', '{error_msg}');</script>",
                    status_code=500
                )
        except:
            pass

        return JSONResponse(
            status_code=500,
            content={"error": {"message": error_msg}}
        )


@router.post("/solutions/{question_id}")
async def update_question_solution(
    request: Request,
    question_id: int,
    solution_data: dict = Body(...),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Update/save an edited question solution
    """
    if login_check:
        return login_check

    try:
        # Prepare solution data for update (including directions)
        update_data = {
            "question": solution_data.get("question", ""),
            "question_type": solution_data.get("question_type", ""),
            "option1": solution_data.get("option1", ""),
            "option2": solution_data.get("option2", ""),
            "option3": solution_data.get("option3", ""),
            "option4": solution_data.get("option4", ""),
            "option5": solution_data.get("option5", ""),
            "answer": solution_data.get("answer", ""),
            "marks": float(solution_data.get("marks")) if solution_data.get("marks") and str(solution_data.get("marks")).strip() else None,
            "negative_mark": float(solution_data.get("negative_mark")) if solution_data.get("negative_mark") and str(solution_data.get("negative_mark")).strip() else None,
            "topic": solution_data.get("topic", ""),
            "subtopic": solution_data.get("subtopic", ""),
            "solution": solution_data.get("solution", ""),
            "directions": solution_data.get("directions", "")  # Include directions in the data
        }

        # Update the solution in database
        success = update_exam_solution(question_id, update_data)

        if success:
            return JSONResponse(
                content={"success": True, "message": "Question updated successfully"}
            )
        else:
            return JSONResponse(
                status_code=500,
                content={"success": False, "error": "Failed to update question"}
            )

    except Exception as e:
        logger.error(f"Error updating question solution {question_id}: {e}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": f"Error updating question: {str(e)}"}
        )


@router.post("/documents/{document_id}/delete")
async def delete_document(
    request: Request,
    document_id: int,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Delete a document and all related files and data
    """
    if login_check:
        return login_check

    try:
        # Get document details
        document = get_exam_document_by_id(document_id)
        if not document:
            return JSONResponse(
                status_code=404,
                content={"error": "Document not found"}
            )

        # Get the PDF path
        pdf_path = document.get("question_paper_path")
        if pdf_path:
            # Get the full S3 path
            full_pdf_path = os.path.join(config.S3_MOUNT_PATH, pdf_path)

            # Get the JSON path for extracted content
            json_path = get_extraction_json_path(pdf_path)
            full_json_path = os.path.join(config.S3_MOUNT_PATH, json_path)

            # Delete the files if they exist
            try:
                if os.path.exists(full_pdf_path):
                    os.remove(full_pdf_path)
                    logger.info(f"Deleted PDF file: {full_pdf_path}")

                if os.path.exists(full_json_path):
                    os.remove(full_json_path)
                    logger.info(f"Deleted JSON file: {full_json_path}")

                # Delete any image files that might have been created
                pdf_dir = os.path.dirname(full_pdf_path)
                pdf_name = os.path.basename(full_pdf_path).split('.')[0]
                image_dir = os.path.join(pdf_dir, f"{pdf_name}_images")
                if os.path.exists(image_dir) and os.path.isdir(image_dir):
                    shutil.rmtree(image_dir)
                    logger.info(f"Deleted image directory: {image_dir}")
            except Exception as e:
                logger.error(f"Error deleting files: {e}")
                # Continue with database deletion even if file deletion fails

        # Delete all solutions for this document
        delete_all_exam_solutions(document_id)

        # Delete the document from the database
        success = remove_exam_document(document_id)
        if not success:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to delete document from database"}
            )

        return JSONResponse(
            content={"success": True, "message": "Document deleted successfully"}
        )

    except Exception as e:
        logger.error(f"Error deleting document: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error deleting document: {str(e)}"}
        )


@router.get("/update-document-status", response_class=HTMLResponse)
async def update_document_status_page(
    request: Request,
    exam_id: int = Query(None),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the update document status page
    """
    if login_check:
        return login_check

    exam = None
    documents = []
    error = None

    if exam_id:
        try:
            # Get exam details
            exam = get_exam_by_id(exam_id)
            if not exam:
                error = f"Exam with ID {exam_id} not found"
            else:
                # Get all documents for this exam (no pagination, no filter)
                documents, _ = get_exam_documents(exam_id, limit=1000, offset=0)
        except Exception as e:
            error = f"Error loading exam data: {str(e)}"

    return templates.TemplateResponse(
        "pyqs_admin/update_document_status.html",
        {
            "request": request,
            "exam_id": exam_id,
            "exam": exam,
            "documents": documents,
            "error": error
        }
    )


@router.post("/update-document-status")
async def update_document_status_api(
    request: Request,
    request_data: dict = Body(...),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Update the status of a document
    """
    if login_check:
        return login_check

    try:
        document_id = request_data.get("document_id")
        status = request_data.get("status")

        if not document_id or not status:
            return JSONResponse(
                status_code=400,
                content={"error": "document_id and status are required"}
            )

        if status not in ["EXTRACTED_ONLY_QUESTIONS", "EXTRACTED_QUES_AND_SOL"]:
            return JSONResponse(
                status_code=400,
                content={"error": "Invalid status value"}
            )

        # Update the document status
        success = update_exam_document_extracted_content_status(document_id, status)

        if success:
            return JSONResponse(content={"success": True, "message": "Status updated successfully"})
        else:
            return JSONResponse(
                status_code=500,
                content={"error": "Failed to update document status"}
            )

    except Exception as e:
        logger.error(f"Error updating document status: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error updating document status: {str(e)}"}
        )
