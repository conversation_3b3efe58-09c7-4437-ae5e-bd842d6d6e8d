from langchain_community.chat_models.perplexity import ChatPerplexity


class PerplexityLLM:
    def __init__(self, api_key, model="sonar", temperature=0):
        self.api_key = api_key
        self.model = model
        self.temperature = temperature

    def get_llm(self, llm_model, req_timeout=600):
        # Use req_timeout if provided, otherwise use default 600
        timeout_value = req_timeout if req_timeout is not None else 600

        return ChatPerplexity(
            model=llm_model if llm_model else self.model,
            temperature=self.temperature,
            pplx_api_key=self.api_key,
            request_timeout=timeout_value
        )
