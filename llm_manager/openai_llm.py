from langchain_openai import ChatOpenAI
from config import llm_config


class OpenAILLM:
    def __init__(self, api_key, model="gpt-4.1-mini", temperature=0):
        self.api_key = api_key
        self.model = model
        self.temperature = temperature
        self.timeout = 360

    def get_llm(self, llm_model, req_timeout=600):
        from llm_manager.llm_factory import LLMFactory
        llm_factory = LLMFactory(llm_config)

        # Use req_timeout if provided, otherwise use default 600
        timeout_value = req_timeout if req_timeout is not None else 600

        return ChatOpenAI(
            model=llm_model if llm_model else self.model,
            temperature=self.temperature,
            openai_api_key=self.api_key,
            max_retries=1,
            request_timeout=timeout_value
        )

